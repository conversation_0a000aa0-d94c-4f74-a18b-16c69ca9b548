import Stomp from "stompjs";
import { Loading, Message } from "element-ui";
import { t } from "@/libs_sz/locale";
/**
 *  options
 *  {
 *     url:"",
 *     autoConnect: true,
 *     connectSuccess(){},
 *     connectFail(){}
 *  }
 *
 *  methods
 *  subcribe
 *  send
 *
 */
const myOptions = {
  autoConnect: true,
};
class myStompSocket {
  constructor(options) {
    this.options = { ...myOptions, ...options };
    this.client = null;
    this.loaded = false;
    this.loadingBox = null;
    this.__connect();
  }
  /** send:{ command, callback, headers} */
  subcribe(send) {
    if (this.client && this.loaded) {
      const sendBody = { headers: null, callback() {}, ...send };
      this.client.subscribe(sendBody.command, sendBody.callback, sendBody.headers && JSON.stringify(sendBody.headers));
    } else {
      console.warn("socket client >>>  connecting");
    }
  }
  send(send) {
    if (this.client && this.loaded) {
      const sendBody = { command: "", headers: {}, body: "", ...send };
      this.client.send(sendBody.command, sendBody.headers, JSON.stringify(sendBody.body));
    }
  }
  __connect() {
    if (!this.options.url) {
      this.__handleError("缺少socket的url");
      return;
    }
    this.client = Stomp.client(this.options.url);
    this.client.connect({ connect: 123 }, this.__onConnected.bind(this));
    this.client.heartbeat.outgoing = 20000;
    this.client.heartbeat.incoming = 0;
  }
  __reConnect() {
    this.loadingBox = Loading.service({
      lock: true,
      text: t("lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer"),
      spinner: "el-icon-loading",
      background: "rgba(255, 255, 255, 0.9)",
    });
    setTimeout(this.connect, 2000);
  }
  __onFailed(msg) {
    this.loaded = false;
    this.options.connectFail && this.options.connectFail();
    this.options.autoConnect && this.__reConnect();
  }
  __onConnected() {
    this.loaded = true;
    this.loadingBox && this.loadingBox.close();
    Message.success(t("lang.ark.fed.successfulConnectionToTheServer"));
    this.options.connectSuccess && this.options.connectSuccess(this.subcribe.bind(this));
  }
  // eslint-disable-next-line class-methods-use-this
  __handleError(msg) {
    console.error(msg);
  }
}
export default myStompSocket;
