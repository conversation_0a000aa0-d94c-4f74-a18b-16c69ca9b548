import axios from "axios";
import * as APIConstants from "@/constants/api";

export function reqStation(params) {
  return axios.get(APIConstants.REQUEST_STATION, {
    params,
  });
}

// RMS 创建地图
export function scanMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_SCANMAP, params);
}
// RMS 创建更新地图
export function getCreatingMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_GETCREATEMAP, params);
}
// RMS 获取地图
export function getMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_GETMAP, params);
}
// RMS 取消建图
export function cancelCreateMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_CANCEL_CREATE_MAP, params);
}
// RMS 完成地图创建
export function stopAndSaveMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_STOPANDSAVEMAP, params);
}
// RMS 获取在线机器人
export function getAllOnlineRobot() {
  return axios.post(APIConstants.REQUEST_ONLINE_ROBOT);
}
// RMS 获取地图上的元素 点线
export function reqGewAttach(params) {
  return axios.post(APIConstants.REQUEST_GET_ATTACH_MAP, params);
}
// RMS 获取地图上的逻辑区
export function getLogicArea(params) {
  return axios.post(APIConstants.REQUEST_MAP_GET_LOGIC_AREA, params);
}
// RMS 获取地图上的工作站
export function getStationArea(params) {
  return axios.get(APIConstants.REQUEST_MAP_GET_STATION_AREA, {
    params,
  });
}
// RMS 联合请求
export function requestAll(requestArr) {
  return axios.all(requestArr);
}
export function saveAttachMap(params) {
  return axios.post(APIConstants.REQUEST_MAP_SAVE_ATTACH, params);
}
// RMS 分类获取地图上的各种点列表
export function getMapNode(params) {
  return axios.post(APIConstants.REQUEST_GET_MAP_NODE, params);
}
// RMS 存逻辑区
export function saveLogicArea(params) {
  return axios.post(APIConstants.REQUEST_SAVE_LOGIC_AREA, params);
}
// RMS 存逻辑区
export function saveStationArea(params) {
  return axios.post(APIConstants.REQUEST_SAVE_STATION_AREA, params);
}

// RMS 删除工作流信息
export function deleteWorkFlow(params) {
  return axios.post(APIConstants.REQUEST_DELETE_WORKFLOW, params);
}

// RMS 获取单个工作流信息
export function getWorkFlow(params) {
  return axios.get(APIConstants.REQUEST_GET_WORKFLOW, { params });
}

// RMS 获取工作流集合信息
export function getWorkFlowList(params) {
  return axios.get(APIConstants.REQUEST_GET_WORKFLOW_LIST, { params });
}

// RMS 获取保存工作流集合信息
export function saveWorkFlow(params) {
  return axios.post(APIConstants.REQUEST_SAVE_WORKFLOW, params);
}

// RMS 系统急停
export function reqStopAll(params) {
  return axios.post(APIConstants.REQUEST_STOP_ALL, params);
}

// RMS 任务恢复
export function reqRestoreAll(params) {
  return axios.post(APIConstants.REQUEST_RESTORE_ALL, params);
}

// 查询货架编码
export function searchShelfCode(params) {
  return axios.get(APIConstants.SEARCH_SHELF_CODE, { params });
}

// 新增货架信息
export function ADD_SHELF_ENTRY(params) {
  return axios.post(APIConstants.ADD_SHELF_ENTRY, params);
}

// 新增货架信息
export function QUERY_SHELF_ENTRY(params) {
  return axios.get(APIConstants.QUERY_SHELF_ENTRY, { params });
}

// 获取货架类型
export function QUERY_ALL_SHELF_CATEGORY(params) {
  return axios.get(APIConstants.QUERY_ALL_SHELF_CATEGORY, { params });
}

// 获取货架类型
export function QUERY_SHELF_PAGE(params) {
  return axios.get(APIConstants.QUERY_SHELF_PAGE, { params });
}

// 保存货架信息
export function SAVE_SHELF(params) {
  return axios.post(APIConstants.SAVE_SHELF, params);
}

// 删除货架信息
export function DELETE_SHELF(params) {
  return axios.post(APIConstants.DELETE_SHELF, params);
}

// 查询在场货架
export function searchShelfInWarehouse(params) {
  return axios.get(APIConstants.SEARCH_SHELF_IN_WAREHOUSE, { params });
}

// 启动异常恢复 强制关闭流程
export function forceCloseAllWorkflow(params) {
  return axios.post(APIConstants.IMITATE_FORCECLOSEALLWORKFLOW, params);
}

// 编辑地图获取区域
export function queryAreaList(params) {
  return axios.get(APIConstants.QUERY_AREA_LIST, { params });
}

// 编辑地图保存区域
export function saveAreaList(params) {
  return axios.post(APIConstants.SAVE_AREA_LIST, params);
}
// 全量无分页货架
export function queryAllShelfByParam(params) {
  return axios.get(APIConstants.QUERY_ALL_SHELF_BY_PARAM, { params });
}

// 更新无校验货架
export function updateShelf(params) {
  return axios.post(APIConstants.UPDATE_SHELF, params);
}

// 同步所有货架 杨柳版本
// export function syncShelves(params) {
//  return axios.post(APIConstants.SYNC_SHELVES, params);
// }

// 同步所有货架 孙凯版本
export function syncShelves(params) {
  return axios.get(APIConstants.SYNC_SHELVES, { params });
}

// 获取 功能区 避障和限速
export function getAllFunctionalArea(params) {
  return axios.post(APIConstants.GET_ALLF_UNCTIONAL_AREA, params);
}

// 保存 功能区 避障和限速
export function saveFunctionalArea(params) {
  return axios.post(APIConstants.SAVE_FUNCTIONALA_REA, params);
}

// 获取按钮指令常量信息
export function queryButtonCommand(params) {
  return axios.get(APIConstants.QUERY_BUTTON_COMMAND, { params });
}
// 获取控制类型常量信息
export function queryButtonControllerType(params) {
  return axios.get(APIConstants.QUERY_BUTTON_CONTROLLER_TYPE, { params });
}
// 获取按钮类型常量信息
export function queryButtonType(params) {
  return axios.get(APIConstants.QUERY_BUTTON_TYPE, { params });
}

// 获取控制器集合信息
export function queryController(params) {
  return axios.get(APIConstants.QUERY_CONTROLLER, { params });
}
// 获取节点类型常量信息
export function queryNodeType(params) {
  return axios.get(APIConstants.QUERY_NODE_TYPE, { params });
}
// 获取操作指令常量信息
export function queryOperationCommand(params) {
  return axios.get(APIConstants.QUERY_OPERATION_COMMAND, { params });
}
// 获取流程类型常量信息
export function queryWorkflowType(params) {
  return axios.get(APIConstants.QUERY_WORKFLOW_TYPE, { params });
}

// 删除控制器信息
export function deleteControllers(params) {
  return axios.post(APIConstants.DELETE_CONTROLLERS, params);
}
// 获取单个控制器信息
export function queryControllerById(params) {
  return axios.get(APIConstants.QUERY_CONTROLLER_BY_ID, { params });
}
// 获取控制器集合信息
export function queryControllerList(params) {
  return axios.get(APIConstants.QUERY_CONTROLLER_LIST, { params });
}
// 保存单个控制器信息（新增编辑都适用）
export function saveController(params) {
  return axios.post(APIConstants.SAVE_CONTROLLER, params);
}

// 查询流程／流程组
export function queryFlowAndGroup(params) {
  return axios.get(APIConstants.QUERY_FLOW_AND_GROUP, { params });
}

// 查询所有非动态模板
export function queryFlowTemplate(params) {
  return axios.get(APIConstants.QUERY_FLOW_TEMPLATE, { params });
}

// 移动货架 api/shelfEntry/moveShelf
export function moveShelf(params) {
  return axios.post(APIConstants.MOVE_SHELF, params);
}

// 策略中心 系统配置, 获取系统配置列表
export function querySysConfigPageList(params) {
  return axios.get(APIConstants.QUERY_SYSCONFIG_PAGE_LIST, { params });
}

// 策略中心 系统配置, 参数分组列表
export function querySysConfigGroupList() {
  return axios.get(APIConstants.QUERY_SYSCONFIG_GROUP_LIST);
}

// 策略中心 系统配置, 保存
export function saveSysConfig(params) {
  return axios.post(APIConstants.SAVE_SYSCONFIG, params);
}

// 容器管理 容器日志 列表数据
export function QUERY_CONTAINER_UPDATE_LOG_FOR_PAGE(params) {
  return axios.get(APIConstants.QUERY_CONTAINER_UPDATE_LOG_FOR_PAGE, { params });
}
