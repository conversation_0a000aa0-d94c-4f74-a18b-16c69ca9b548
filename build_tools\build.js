const fs = require('fs');
const exec = require('child_process').exec;
const spawn = require('child_process').spawn;

let config;
let index = 0;
const baseUrl = '../../';

function getConfig() {
  config = fs.readFileSync('./config.json', 'UTF-8');
  config = JSON.parse(config);
  checkoutBranch();
}

function checkoutBranch() {
  console.log('=========================================');
  const path = baseUrl + config.dirName;
  console.log(`切换目标分支 -- ${config.dirName}`);

  const child = exec(`cd ${path} && pwd && git pull && git checkout ${config.branch} && git pull`);

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`Error: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('操作结束');
      console.log('=========================================');
      clearBuild();
    } else {
      console.log(`closing code: ${code}`);
      createBranch();
    }
  });
}

function createBranch() {
  console.log('=========================================');
  const path = baseUrl + config.dirName;
  console.log(`创建目标分支 -- ${config.dirName}`);

  const child = exec(`cd ${path} && pwd && git pull && git checkout -b ${config.branch} && git push --set-upstream origin ${config.branch}`);

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`stderr: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('操作结束');
      console.log('=========================================');
      clearBuild();
    } else {
      console.log(`closing code: ${code}`);
    }
  });
}

function clearBuild() {
  console.log('=========================================');
  const path = baseUrl + config.dirName;
  console.log(`清空 build 目录 -- ${config.dirName}`);

  const child = exec(`cd ${path} && pwd && rm -rf ${config.projects[0].targetName}`);

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`Error: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('操作结束');
      console.log('=========================================');
      build();
    } else {
      console.log(`closing code: ${code}`);
    }
  });
}

function build() {
  if (index >= config.projects.length) {
    finish();
    return;
  }
  const item = config.projects[index];
  const isMain = (index === 0);
  switch (item.type) {
    case 'gulp':
      doGulpBuild(item, isMain);
      break;
    case 'vue':
      doVueBuild(item, isMain);
      break;
  }
  index += 1;
}


function doGulpBuild(item) {
  console.log('=========================================');
  const path = baseUrl + item.dirName;
  console.log(`当前操作路径"${path}"`);

  const child = exec(`cd ${path} && pwd && git checkout ${item.branch} && git pull && gulp build && mv ./${item.buildDir}  ../${config.dirName}/${item.targetName}`);

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`stderr: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('操作结束');
      console.log('=========================================');
      build();
    } else {
      console.log(`closing code: ${code}`);
    }
  });
}

function doVueBuild(item, isMain) {
  console.log('=========================================');
  const path = baseUrl + item.dirName;
  console.log(`当前操作路径"${path}"`);
  let targetPath = '';
  if (isMain) {
    targetPath = `../${config.dirName}/${item.targetName}`;
  } else {
    targetPath = `../${config.dirName}/${config.projects[0].targetName}/${item.targetName}`;
  }

  const child = exec(`cd ${path} && pwd && git checkout ${item.branch} && git pull && npm run build && mv ./${item.buildDir} ${targetPath}`);

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`stderr: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('操作结束');
      console.log('=========================================');
      build();
    } else {
      console.log(`closing code: ${code}`);
    }
  });
}

function finish() {
  console.log('=========================================');
  console.log('提交git');

  const path = baseUrl + config.dirName;

  const child = exec(`cd ${path} && pwd && git add . && git commit -am"build branch:${config.branch}" && git push origin ${config.branch}`, {
    maxBuffer: 1024 * 1024,
  });

  child.stdout.on('data', (data) => {
    console.log(data);
  });
  child.stderr.on('data', (data) => {
    console.log(`stderr: ${data}`);
  });
  child.on('close', (code) => {
    if (code == 0) {
      console.log('打包完成');
      console.log('=========================================');
    } else {
      console.log(`closing code: ${code}`);
    }
  });
}

function init() {
  getConfig();
}

init();
