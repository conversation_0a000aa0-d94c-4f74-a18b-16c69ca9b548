import config from "@/common/config";

const { arkRoot } = config;

export default {
  mapNodeRelation: {
    queryMapNodeRelationByPage: {
      method: "get",
      url: `${arkRoot}/api/mapNodeRelation/queryMapNodeRelationByPage`,
      describe: "查询接口",
      isNeedLoading: false,
    },
    saveMapNodeRelation: {
      method: "post",
      url: `${arkRoot}/api/mapNodeRelation/saveMapNodeRelation`,
      describe: "单个保存/禁用接口",
      isNeedLoading: false,
    },
    insertMapNodeRelation: {
      method: "post",
      url: `${arkRoot}/api/mapNodeRelation/insertMapNodeRelation`,
      describe: "批量添加保存",
      isNeedLoading: false,
    },
    queryMapNodeRelation: {
      method: "get",
      url: `${arkRoot}/api/mapNodeRelation/queryMapNodeRelation`,
      describe: "查询单个点位对照",
      isNeedLoading: false,
    },
    deleteMapNodeRelation: {
      method: "get",
      url: `${arkRoot}/api/mapNodeRelation/deleteMapNodeRelation`,
      describe: "删除",
      isNeedLoading: false,
    },
    executeNodeRelationTask: {
      method: "get",
      url: `${arkRoot}/api/mapNodeRelation/executeNodeRelationTask`,
      describe: "执行",
      isNeedLoading: false,
    },
  },
};
