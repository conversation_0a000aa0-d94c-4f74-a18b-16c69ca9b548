import Vue from "vue";
import WS from "@/common/websocket";
import { mapMutations, mapActions } from "vuex";
import config from "@/common/config";
import requestRobotStatus from "@/common/requestRobotStatus";
import * as websocketMsgType from "@/constants/websocketMsgType";
import i18n from "@/i18n/i18n";
import eventEmitter from "./eventEmitter";

export default {
  data() {
    return {
      isDataReady: false,
      loading: null,
    };
  },
  computed: {},
  beforeDestroy() {
    this.initWS_Distory();
  },
  methods: {
    initWS_Register() {
      console.log("/*/*/*/*/*/*/*****/*****/*/*/*/*init WS ");
      Vue.prototype.$websocket = new WS(config.RMSURL);
      this.requestRobotStatus = requestRobotStatus(this.$websocket);
      // 总线事件注册
      this.registerWSOpenEvent();
      this.registerWScloseEvent();
      this.registerWSMessageEvent();
      this.registerWSSendEvent();
    },
    initWS_Distory() {
      console.log("2,监控和编辑页面切换-=-=-=-=-=-=-=-==-=-=");
      eventEmitter.emit("wsclose");
      eventEmitter.removeAllListeners("wsopen");
      eventEmitter.removeAllListeners("wsclose");
      eventEmitter.removeAllListeners("wsmessage");
      eventEmitter.removeAllListeners("wssend");
      if (this.loading) this.loading.close();
    },
    handleWebsocket(data) {
      const { msgType } = data;

      console.log("与服务器通信中..");
      // this.requestRobotStatus.close().then(this.handleCurrentTaskEnded);
      switch (msgType) {
        // 获取机器人状态
        case websocketMsgType.RESPONSE_ROBOT_POSITION:
          eventEmitter.emit("updateRobot", data);
          break;
        default:
          this.requestRobotStatus.start().then(this.handleCurrentTaskStarted);
          break;
      }
    },
    registerWSOpenEvent() {
      eventEmitter.on("wsopen", () => {
        console.log("wsopen !@#!@#");
        if (this.loading) this.loading.close();
        this.$message({
          message: `${i18n.t("lang.ark.fed.successfulConnectionToTheServer")}!`,
          type: "success",
        });
        this.requestRobotStatus.start().then(this.handleCurrentTaskStarted);
      });
    },
    registerWScloseEvent() {
      eventEmitter.on("wsclose", () => {
        // console.log('$t('lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer')');
        this.loading = this.$loading({
          lock: true,
          text: i18n.t("lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer"),
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.9)",
        });
        this.requestRobotStatus.close().then(this.handleCurrentTaskEnded);
        setTimeout(() => {
          this.$websocket.restart();
        }, 1000);
      });
    },
    registerWSMessageEvent() {
      eventEmitter.on("wsmessage", (data) => {
        this.handleWebsocket(data);
      });
    },
    registerWSSendEvent() {
      eventEmitter.on("wssend", (data) => {
        // console.log(data);
        // 接受盘点任务完成
        // if (data.msgType === websocketMsgType.REQUEST_COMPLETE_TAKE_STOCK_TASK) {
        //   console.log('====获取当前任务');
        //   this.isDataReady = false;
        //   this.setScanCount('clear');
        //   this.setPrevPackingCount(null);
        // }
      });
    },
    handleRobotPosition(data) {
      console.log(data);
    },
    errorHandler(data) {
      return new Promise((resolve, reject) => {
        if (data.code === 0) {
          resolve();
        } else {
          this.$message({
            type: "error",
            message: data.msg,
            duration: 3000,
            center: true,
          });
          reject();
        }
      });
    },
    ...mapMutations({}),
    ...mapActions([]),
  },
};
