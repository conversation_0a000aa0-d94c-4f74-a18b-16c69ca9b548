/*! <AUTHOR> at 2021/04 */

const execSync = require("child_process").execSync; // 同步子进程
const simpleGit = require('simple-git')
const fs = require("fs");
const path = require("path");

const commitId = execSync("git show -s --format=%h")
  .toString()
  .trim(); // commitId
const auth = execSync("git show -s --format=%cn")
  .toString()
  .trim(); // 姓名
const date = new Date(execSync("git show -s --format=%cd").toString()); // 日期
const message = execSync("git show -s --format=%s")
  .toString()
  .trim(); // 说明

function formatDate(date, fmt) {
  const o = {
    "M+": date.getMonth() + 1, // 月份
    "d+": date.getDate(), // 日
    "h+": date.getHours(), // 小时
    "m+": date.getMinutes(), // 分
    "s+": date.getSeconds(), // 秒
    "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, `${date.getFullYear()}`.substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : `00${o[k]}`.substr(`${o[k]}`.length));
    }
  }
  return fmt;
}

const git = simpleGit(path.resolve(__dirname, '../../'));
git.init().status().then(({ current }) => {
  const config = {
    tag: current,
    commitId,
    auth,
    date: formatDate(date, "yyyy-MM-dd hh:mm"),
    message,
  };

  const VERSION_CONF_PATH = "./static/version.config.json";
  if (fs.existsSync(VERSION_CONF_PATH)) fs.unlinkSync(VERSION_CONF_PATH); // 如果已存在此文件，则删除
  fs.writeFileSync(VERSION_CONF_PATH, JSON.stringify(config, null, "\t"));
});



