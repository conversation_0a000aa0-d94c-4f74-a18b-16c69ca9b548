import * as api from "@/constants/api";
import axios from "axios";

// 获取停靠点列表
export function getStopPointList(params) {
  return axios.post(api.GET_STOP_POINT, params);
}

// 获取当前工作流信息
export function getWorkFlowInfo(params) {
  return axios.post(api.GET_WORKFLOW_INFO, params);
}

// 获取货架类型列表
export function getShelfCategoryList(params) {
  return axios.post(api.GET_SHELF_CATEGORY_LIST, params);
}

// 获取货架类型列表
export function getShelfList(params) {
  return axios.post(api.GET_SHELF_LIST, params);
}

// 获取工作流列表
export function getWorkFlowList(params) {
  return axios.post(api.GET_WORKFLOW_LIST, params);
}

// 获取工作流/工作组列表
export function queryWorkflowGroupList(params) {
  return axios.post(api.QUERY_WORKFLOW_GROUPLIST, params);
}

// 获获取流程或流程组当前节点得明细
export function queryWorkflowGroupDetail(params) {
  return axios.post(api.QUERY_WORKFLOW_DETAIL, params);
}

// 获取工作流节点列表
export function getWorkFlowNodeList(params) {
  return axios.post(api.GET_WORKFLOW_Node_LIST, params);
}

// 查询任务类型信息
export function queryOperateTypeInfo(params) {
  return axios.get(api.QUERY_OPERATETYPE_INFO, {
    params,
  });
}

// 查询工作站日志信息
export function queryWorkStationLogInfo(params) {
  return axios.get(api.QUERY_WORKSTATIONLOG_INFO, {
    params,
  });
}

// 查询任务日志信息接口
export function queryWorkflowTaskLogList(params) {
  return axios.get(api.QUERY_WORKFLOW_LOG_List, {
    params,
  });
}

// 查询流程任务类型信息
export function queryWorkflowTaskTypeInfo(params) {
  return axios.get(api.QUERY_WORKFLOW, {
    params,
  });
}

// 获取机器人信息
export function queryRobotIdsInfo(params) {
  return axios.get(api.QUERY_ROBOTID_INFO, {
    params,
  });
}

// 取货架
export function fetchShelf(params) {
  return axios.post(api.FETCH_SHELF, params);
}

// 送货架
export function sendShelf(params) {
  return axios.post(api.SEND_SHELF, params);
}

// 自动执行
export function auto(params) {
  return axios.post(api.AUTO, params);
}

// 自动执行
export function workflowStation(params) {
  return axios.get(api.WORKFLOW_STATION, {
    params,
  });
}

// 工作站系统急停
export function pauseSystem(params) {
  return axios.post(api.WORKSTATION_PAUSESYSTEM, params);
}

// 工作站系统恢复
export function resumeSystem(params) {
  return axios.post(api.WORKSTATION_RESUMESYSTEM, params);
}

// 任务管理
export function queryUnCompletedWorkflowTaskList(params) {
  return axios.get(api.QUERY_UNCOMPLETED_WORKFLOW_TASK_LIST, { params });
}

// 任务管理-调整优先级
export function updateWorkflowPriority(params) {
  return axios.post(api.UPDATE_WORKFLOW_PRIORITY, params);
}
// 任务管理-取消
export function cancelWorkflow(params) {
  return axios.post(api.CANCEL_WORKFLOW, params);
}
// 任务管理-批量取消
export function batchCancelWorkflow(params) {
  return axios.post(api.BATCH_CANCEL_WORKFLOW, params);
}
// 流程组选择工作流下拉选项
export function getWorkflowSelector(params) {
  return axios.get(api.QUERY_WORKFLOW_SELECT, { params });
}

// 保存流程组配置
export function saveWorkflowConfigInfo(params) {
  return axios.post(api.SAVE_WORKCONFIGCONFIG_INFO, params);
}

// 获取流程组信息
export function selectWorkflowGroupList(params) {
  return axios.get(api.SELECT_WORKFLOWGROUP_LIST, { params });
}

// 获取流程组是否启用下拉框
export function selectEnableFlagOptions(params) {
  return axios.get(api.SELECT_ENABLEFLAG_OPTIONS, { params });
}

// 获取流程组是否启用下拉框
export function selectIsCircleOptions(params) {
  return axios.get(api.SELECT_ISCIRCLE_OPTIONS, { params });
}

// 根据流程组ID查明细
export function selectWorkflowGroupConfig(params) {
  return axios.get(api.SELECT_WORKFLOWGROUP_CONFIG, { params });
}

// 根据流程组ID查明细
export function updateWorkFlowCondition(params) {
  return axios.post(api.UPDATE_WORKFLOW_CONDITION, params);
}

// 查看全流程
export function queryTotalWorkConfigInfo(params) {
  return axios.post(api.QUERY_TOTAL_WORKCONFIGINFO, params);
}

// 查看流程组相关得工作流任务集合
export function queryGroupRefWorkflowTaskList(params) {
  return axios.get(api.QUERY_GROUPREFWORKFLOWTASK_LIST, { params });
}

export function logoutWorkstation(params) {
  return axios.get(api.LOGOUT_STATION, { params });
}
