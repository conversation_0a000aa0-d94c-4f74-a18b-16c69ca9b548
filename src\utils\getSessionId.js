import { Loading } from "element-ui";
import axios from "axios"; // 在引用组件
import myConfig from "@/common/config";
import store from "@/store";
import { getLang } from "@/libs_sz/plugins/loadLang";

/**
 *  登录逻辑说明
 *
 *  1. 过滤所有的页面切换，都要请求sessionId, 如果成功可以打开下一个页面，如果不成功，是否异常，是异常，则不带returnUrl, 不是异常则跳转到登录带returnUrl,
 *
 *  2. 如果sessionId接口判断没有权限，则跳转403，并且修改退出，不带returnUrl;
 *
 */

// 登录操作
const toLogin = (cred) => store.dispatch("toLogin", { cred });

// 根据系统参数配置的登录方式，决定登录url
let loginUrl = myConfig.loginType === "CARD_NO" ? myConfig.loginByCardNoUrl : myConfig.loginUrl;
// 跳转到登录
const toLoginPage = (isReturn = true, to) => {
  let [host, argument] = location.href.split("?");
  // 如果点击了下一个菜单，则修改当前路径
  if (to) {
    host = `${host.split("#")[0]}#${to.fullPath}`;
  }
  if (isReturn) {
    let url = host;
    if (argument) {
      url += "?";
      // 过滤cred
      const params = [].concat(argument.split("&"));
      const validParams = params.filter((item) => !~item.indexOf("cred") || !~item.indexOf("system"));
      url += validParams.join("&");
    }
    location.href = `${loginUrl}?systemCode=${store.state.systemCode}&r=${encodeURIComponent(url)}`;
  } else {
    location.href = loginUrl;
  }
};

// 跳转到403页面
const to403 = () => {
  location.href = `${myConfig.authNoPerUrl}?systemCode=${store.state.systemCode}`;
};

export const linkAuthMange = toLoginPage;
export const isGetSessionId = (to) =>
  new Promise((resolve) => {
    axios
      .get(`${myConfig.authUrl}/api/coreresource/auth/token/getSession/v1`, {
        headers: {
          "Accept-Language": getLang(),
        },
        withCredentials: true,
      })
      .then(({ data, code }) => {
        console.log("🔍 认证接口响应:", { data, code });
        const SYSTEM = store.state.systemCode;
        // 如果发生错误直接跳转登录页
        if (code) {
          console.error("❌ 认证接口返回错误码:", code);
          toLoginPage(true, to);
          return;
        }
        const { subsystemList, sessionId, isAuthenticated } = data;
        console.log("🔍 认证数据:", { subsystemList, sessionId, isAuthenticated, systemCode: SYSTEM });
        if (!sessionId || !isAuthenticated) {
          console.error("❌ SessionId或认证状态无效:", { sessionId, isAuthenticated });
          toLoginPage(true, to);
          return;
        }
        const hasPermission = (subsystemList || []).find((item) => item.code === SYSTEM);
        console.log("🔍 权限检查:", { hasPermission, systemCode: SYSTEM, subsystemList });
        // 如果没有权限则直接跳转403页面；
        if (!hasPermission) {
          console.error("❌ 用户没有系统权限:", SYSTEM);
          to403();
          return;
        }

        localStorage.setItem("Gek-Authorization", data.sessionId);

        // 其他则判断当前是否在登录状态；
        const old = store.state.cred;
        console.log("🔍 登录状态检查:", { oldCred: old, newSessionId: sessionId });
        // 如果旧的cred不存在， 或者 旧的cred不等于新的cred
        if (!old || old !== sessionId) {
          console.log("🔄 执行登录操作...");
          const loading = Loading.service({
            fullscreen: true,
            lock: true,
            text: "logining...",
          });
          toLogin(sessionId)
            .then(() => {
              console.log("✅ 登录成功");
              loading.close();
              resolve();
            })
            .catch((error) => {
              console.error("❌ 登录失败:", error);
              loading.close();
              toLoginPage(false);
            });
        } else {
          console.log("✅ 已登录，直接通过");
          resolve();
        }
      })
      .catch((error) => {
        console.error("❌ 认证接口请求失败:", error);
        toLoginPage(false);
      });
  });
export const noPermission = to403;
