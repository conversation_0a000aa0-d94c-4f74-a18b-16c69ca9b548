import { Loading } from "element-ui";
import axios from "axios"; // 在引用组件
import myConfig from "@/common/config";
import store from "@/store";
import { getLang } from "@/libs_sz/plugins/loadLang";

/**
 *  登录逻辑说明
 *
 *  1. 过滤所有的页面切换，都要请求sessionId, 如果成功可以打开下一个页面，如果不成功，是否异常，是异常，则不带returnUrl, 不是异常则跳转到登录带returnUrl,
 *
 *  2. 如果sessionId接口判断没有权限，则跳转403，并且修改退出，不带returnUrl;
 *
 */

// 登录操作
const toLogin = (cred) => store.dispatch("toLogin", { cred });

// 根据系统参数配置的登录方式，决定登录url
let loginUrl = myConfig.loginType === "CARD_NO" ? myConfig.loginByCardNoUrl : myConfig.loginUrl;
// 跳转到登录
const toLoginPage = (isReturn = true, to) => {
  let [host, argument] = location.href.split("?");
  // 如果点击了下一个菜单，则修改当前路径
  if (to) {
    host = `${host.split("#")[0]}#${to.fullPath}`;
  }
  if (isReturn) {
    let url = host;
    if (argument) {
      url += "?";
      // 过滤cred
      const params = [].concat(argument.split("&"));
      const validParams = params.filter((item) => !~item.indexOf("cred") || !~item.indexOf("system"));
      url += validParams.join("&");
    }
    location.href = `${loginUrl}?systemCode=${store.state.systemCode}&r=${encodeURIComponent(url)}`;
  } else {
    location.href = loginUrl;
  }
};

// 跳转到403页面
const to403 = () => {
  location.href = `${myConfig.authNoPerUrl}?systemCode=${store.state.systemCode}`;
};

export const linkAuthMange = toLoginPage;
export const isGetSessionId = (to) =>
  new Promise((resolve) => {
    axios
      .get(`${myConfig.authUrl}/api/coreresource/auth/token/getSession/v1`, {
        headers: {
          "Accept-Language": getLang(),
        },
        withCredentials: true,
      })
      .then(({ data, code }) => {
        const SYSTEM = store.state.systemCode;
        // 如果发生错误直接跳转登录页
        if (code) {
          debugger; // 断点1: 检查认证接口返回的错误码
          console.log("🚨 认证接口返回错误码:", code);
          toLoginPage(true, to);
          return;
        }
        const { subsystemList, sessionId, isAuthenticated } = data;
        if (!sessionId || !isAuthenticated) {
          debugger; // 断点2: 检查sessionId和认证状态
          console.log("🚨 sessionId或认证状态无效:", { sessionId, isAuthenticated });
          toLoginPage(true, to);
          return;
        }
        const hasPermission = (subsystemList || []).find((item) => item.code === SYSTEM);
        // 如果没有权限则直接跳转403页面；
        if (!hasPermission) {
          debugger; // 断点3: 检查用户权限
          console.log("🚨 用户没有系统权限:", { SYSTEM, subsystemList, hasPermission });
          to403();
          return;
        }

        localStorage.setItem("Gek-Authorization", data.sessionId);

        // 其他则判断当前是否在登录状态；
        const old = store.state.cred;
        // 如果旧的cred不存在， 或者 旧的cred不等于新的cred
        if (!old || old !== sessionId) {
          const loading = Loading.service({
            fullscreen: true,
            lock: true,
            text: "logining...",
          });
          toLogin(sessionId)
            .then(() => {
              loading.close();
              resolve();
            })
            .catch((error) => {
              debugger; // 断点4: 登录失败
              console.log("🚨 登录失败:", error);
              toLoginPage(false);
            });
        } else {
          resolve();
        }
      })
      .catch(() => toLoginPage(false));
  });
export const noPermission = to403;
