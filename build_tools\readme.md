```
{
    // 项目名称
    "project":"gaea-fe-build",
    // build 目标目录
    "dirName":"gaea-fe-build",
    // build 目标分支
    "branch":"dev",
    // build 包含的项目
    "projects":[
        {
            // 项目目录名称
            "dirName":"gaea-fe",
            // 项目分支
            "branch":"sandbox",
            // 项目build目录
            "buildDir":"build",
            // 项目在目标目录的路径
            "targetDir":"./",
            // 项目在目标目录的文件夹名
            "targetName":"build",
            // 项目类型
            "type":"gulp"
        },
        {
            "dirName":"review-fe",
            "branch":"dev-3.0",
            "buildDir":"dist",
            "targetDir":"./build",
            "targetName":"review-fe",
            "type":"vue"
        }
    ]
}
```


执行命令

```
node build.mjs
```

注意事项

要求项目都是同一级目录中，名字为git的项目名

