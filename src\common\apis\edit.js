import config from "@/common/config";

const { arkRoot } = config;

export default {
  // 页面地址
  edit: {
    checkWhenDelete: {
      method: "get",
      url: `${arkRoot}/api/constant/checkWhenDelete`,
      describe: "删除地图元素前置检查",
      isNeedLoading: false,
    },
    checkWhenUnBind: {
      method: "get",
      url: `${arkRoot}/api/constant/checkWhenUnBind`,
      describe: "解绑最后一个元素时查询是否绑定流程",
      isNeedLoading: true,
    },
  },
};
