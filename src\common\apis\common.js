import config from "@/common/config";

const { arkRoot, athenaRoot, authUrl } = config;

export default {
  common: {
    getLanguages: {
      method: "get",
      url: `${arkRoot}/api/coreresource/i18n/getLanguages/v1`,
      describe: "获取语言类型",
      isNeedLoading: false,
    },
    getLangItems: {
      method: "get",
      url: `${arkRoot}/api/coreresource/i18n/getLangItems/v1`,
      describe: "获取翻译字段",
      isNeedLoading: false,
    },
    queryDetails: {
      method: "post", // 结构请求的方式
      url: `${arkRoot}/api_v1/dict/batchQuery`, // 接口URL
      describe: "获取字典", // 标注
      isNeedLoading: false, // 是否需要loading
    },
    athenaGetLangItems: {
      method: "post", // 结构请求的方式
      url: `${athenaRoot}/api/coreresource/i18n/getLangItems/v1`, // 接口URL
      describe: "获取字典", // 标注
      isNeedLoading: false, // 是否需要loading
    },
    getAuthManageLangItems: {
      method: "post", // 结构请求的方式
      url: `${authUrl}/api/coreresource/i18n/getLangItems/v1`, // 接口URL
      describe: "获取权限字典", // 标注
      isNeedLoading: false, // 是否需要loading
    },
  },
};
