import config from "@/common/config";

const { arkRoot } = config;

export default {
  maintain: {
    blockTask: {
      method: "get",
      url: `${arkRoot}/maintain/blockTask`,
      describe: "流程创建提示目标点不空闲任务列表接口",
      isNeedLoading: false,
    },

    overtimeTask: {
      method: "get",
      url: `${arkRoot}/maintain/overtimeTask`,
      describe: "监控异常任务列表接口",
      isNeedLoading: false,
    },

    waitTask: {
      method: "get",
      url: `${arkRoot}/maintain/waitTask`,
      describe: "等待点任务列表接口",
      isNeedLoading: false,
    },

    removeCellLock: {
      method: "get",
      url: `${arkRoot}/api/lock/removeCellLock`,
      describe: "清除货架缓存",
      isNeedLoading: false,
    },

    clearWaitPoint: {
      method: "get",
      url: `${arkRoot}/maintain/clearWaitPoint`,
      describe: "手动放行",
      isNeedLoading: false,
    },
  },
};
