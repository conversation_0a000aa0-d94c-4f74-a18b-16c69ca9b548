import homeNew from "@/assets/images/homeNew.jpg";
import loginLogo from "@/assets/images/login_logo.png";
import smallLogo from "@/assets/images/small_logo.png";

const PREFIX = location.pathname;

const BG_URL = {
  system_arkHome: {
    url: PREFIX + "static/system_arkHome.jpg",
    def: homeNew,
  },
  system_logo: {
    url: PREFIX + "static/system_logo.png",
    def: loginLogo,
  },
  system_icon: {
    url: PREFIX + "static/system_icon.png",
    def: smallLogo,
  },
};
window.BG_URL = BG_URL;

// 如果url存在资源则
export const getImageUrl = (name) => {
  return new Promise((resolve) => {
    const homeViewImage = new Image();
    const { def, url } = BG_URL[name];

    homeViewImage.src = url;
    homeViewImage.onload = () => {
      resolve(`${url}?v=${+new Date()}`);
    };

    // TODO: 如果是base64则不增加时间戳

    homeViewImage.onerror = () => {
      resolve(def.startsWith("data:image") ? def : `${def}?v=${+new Date()}`);
    };
  });
};
