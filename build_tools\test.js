// 兼容window平台打包
const fs = require('fs');
const fse = require('fs-extra');
const exec = require('child_process').exec;
const os = require('os');
const pathMoudle = require('path');
// 判断是否是Linx系统
const isLinux = os.type() === 'Linux';
const prompt = require('inquirer').prompt;

let config;
let index = 0;
// 全部统一到根目录
const dirName =
    pathMoudle.basename(process.cwd()) === 'build_tools' ? '../' : './';
const buildBase = pathMoudle.resolve(dirName, '../');


function getConfig() {
    exec('git symbolic-ref --short -q HEAD', (error, stdout, stderr) => {
        config = fs.readFileSync(
            pathMoudle.resolve(dirName, 'build_tools/config.json'),
            'UTF-8',
        );
        config = JSON.parse(config);
        if (stdout.trim() !== config.branch.trim() || stdout.trim() !== config.projects[0].branch.trim()) {
            prompt([{
                type: 'confirm',
                name: 'test',
                message: '> config.json配置的branch分支和当前branch不匹配，是否继续？',
                default: false,
            }]).then(() => createBranch());
        } else {
            checkoutBranch();
        }
    });
}

function checkoutBranch() {
    console.log('=========================================');
    const path = pathMoudle.resolve(buildBase, config.dirName);
    console.log(`切换目标分支 -- ${config.dirName}`);

    const child = exec(
        `cd ${path} ${isLinux ? '&& pwd' : ''} && git pull && git checkout ${
      config.branch
    } && git pull`,
    );

    child.stdout.on('data', (data) => {
        console.log(data);
    });
    child.stderr.on('data', (data) => {
        console.log(`Error: ${data}`);
    });
    child.on('close', (code) => {
        if (code == 0) {
            console.log('操作结束');
            console.log('=========================================');
            clearBuild();
        } else {
            console.log(`closing code: ${code}`);
            createBranch();
        }
    });
}

function createBranch() {
    console.log('=========================================');
    const path = pathMoudle.resolve(buildBase, config.dirName);
    console.log(`创建目标分支 -- ${config.dirName}`);

    const child = exec(
        `cd ${path}  ${isLinux ? '&& pwd' : ''} && git pull && git checkout -b ${
      config.branch
    } && git push --set-upstream origin ${config.branch}`,
    );

    child.stdout.on('data', (data) => {
        console.log(data);
    });
    child.stderr.on('data', (data) => {
        console.log(`stderr: ${data}`);
    });
    child.on('close', (code) => {
        if (code == 0) {
            console.log('操作结束');
            console.log('=========================================');
            clearBuild();
        } else {
            console.log(`closing code: ${code}`);
        }
    });
}

function clearBuild() {
    console.log('=========================================');
    const path = pathMoudle.resolve(buildBase, config.dirName);
    console.log(`清空 build 目录 -- ${config.dirName}`);
    if (isLinux) {
        const child = exec(
            `cd ${path} && pwd && rm -rf ${config.projects[0].targetName}`,
        );

        child.stdout.on('data', (data) => {
            console.log(data);
        });
        child.stderr.on('data', (data) => {
            console.log(`Error: ${data}`);
        });
        child.on('close', (code) => {
            if (code == 0) {
                console.log('操作结束');
                console.log('=========================================');
                build();
            } else {
                console.log(`closing code: ${code}`);
            }
        });
    } else {
        // window平台， 用node实现，主要本人没MAC电脑测试，所以保险起见，使用if/else
        const delDir = (linkPath) => {
            let files = [];
            if (fs.existsSync(linkPath)) {
                files = fs.readdirSync(linkPath);
                files.forEach((file) => {
                    const curPath = `${linkPath}/${file}`;
                    if (fs.statSync(curPath).isDirectory()) {
                        delDir(curPath);
                    } else {
                        fs.unlinkSync(curPath);
                    }
                });
                fs.rmdirSync(linkPath);
            }
        };
        delDir(pathMoudle.resolve(path, config.projects[0].targetName));
        build();
    }
}

function build() {
    if (index >= config.projects.length) {
        finish();
        return;
    }
    const item = config.projects[index];
    const isMain = index === 0;
    switch (item.type) {
        case 'gulp':
            doGulpBuild(item, isMain);
            break;
        case 'vue':
            doVueBuild(item, isMain);
            break;
    }
    index += 1;
}

function doGulpBuild(item) {
    console.log('=========================================');
    const path = pathMoudle.resolve(buildBase, config.dirName);
    console.log(`当前操作路径"${path}"`);

    const child = exec(
        `cd ${path} ${isLinux ? '&& pwd' : ''} && git checkout ${
      item.branch
    } && git pull && gulp build && mv ./${item.buildDir}  ../${
      config.dirName
    }/${item.targetName}`,
    );

    child.stdout.on('data', (data) => {
        console.log(data);
    });
    child.stderr.on('data', (data) => {
        console.log(`stderr: ${data}`);
    });
    child.on('close', (code) => {
        if (code == 0) {
            console.log('操作结束');
            console.log('=========================================');
            build();
        } else {
            console.log(`closing code: ${code}`);
        }
    });
}

function doVueBuild(item, isMain) {
    console.log('=========================================');
    const arkFePath = pathMoudle.resolve(buildBase, item.dirName);
    const arkBuildPath = pathMoudle.resolve(buildBase, config.dirName);
    console.log(`当前操作路径"${arkFePath}"`);
    let targetPath = '';
    if (isMain) {
        targetPath = pathMoudle.resolve(arkBuildPath, item.targetName);
    } else {
        targetPath = pathMoudle.join(
            arkBuildPath,
            config.projects[0].targetName,
            item.targetName,
        );
    }
    exec(
        `cd ${arkFePath} ${isLinux ? '&& pwd' : ''} && git checkout ${
      item.branch
    } && git pull && npm run build`,
        (error, stdout, stderr) => {
            const buildDir = pathMoudle.resolve(arkFePath, item.buildDir);
            if (error) {
                console.log(stderr);
            } else if (isLinux) {
                exec(`mv ${buildDir} ${targetPath}`, (err, std, message) => {
                    if (err) {
                        console.log(message);
                    } else {
                        build();
                    }
                });
            } else {
                const oldPath = pathMoudle.resolve(arkFePath, item.buildDir);
                const newPath = targetPath;
                // fs.rename(
                //     oldPath,
                //     newPath,
                //     (err) => {
                //         if (err) {
                //             console.log('rename报错>>>>', err);
                //             if (err.code === 'EXDEV') {
                //                 const readStream = fs.createReadStream(oldPath);
                //                 const writeStream = fs.createWriteStream(newPath);
                //                 readStream.on('close', function() {
                //                     fs.unlink(oldPath);
                //                 });
                //                 readStream.pipe(writeStream);
                //             } else {
                //                 throw err;
                //             }
                //         };
                //         build();
                //     },
                // );
                fse.copy(oldPath, newPath, err => {
                    if (err) throw err;
                    build();
                });
            }
        },
    );
}

function finish() {
    console.log('=========================================');
    console.log('提交git');

    const path = pathMoudle.resolve(buildBase, config.dirName);

    const child = exec(
        `cd ${path} ${
      isLinux ? '&& pwd' : ''
    } && git add . && git commit -am"build branch:${
      config.branch
    }" && git push origin ${config.branch}`, {
            maxBuffer: 1024 * 1024,
        },
    );

    child.stdout.on('data', (data) => {
        console.log(data);
    });
    child.stderr.on('data', (data) => {
        console.log(`stderr: ${data}`);
    });
    child.on('close', (code) => {
        if (code == 0) {
            console.log('打包完成');
            console.log('=========================================');
        } else {
            console.log(`closing code: ${code}`);
        }
    });
}

function init() {
    getConfig();
}

init();