/**
 *  应对的场景，一个标准功能或者bug，合并到多个分支，并且打包到对应的ark-build
 *
 *  一、逻辑介绍：
 *  1. git已暂存内容自动提交，并且合并至所有分支
 *  2. 如果是bug类型的，
 *     1. 如果是全局bug,  则从ark-dev啦上，然后合并到所有需要合并的分支
 *     2. 如果是某个功能分支导致的bug, 则在功能分支上修复，并且合并到ark-test
 *     3. 如果某个功能分支测试完成，进入回归阶段， 则还是在那个功能分支合并，在合到ark-test/ ark-fe-rc-1.x.x.x;
 *     4. 未解决问题， ark-dev 已经 比 出现bug的rc分支版本高，并且是个通用bug; 此建议，从低版本啦个bug版本，往所有分支合；
 *
 *  二、注意点：
 *  1. 必须把需要合并到所有分支的内容提交到暂存区内。
 *  2. 有冲突的时候先解决冲突，并且添加到暂存区，并且回答，“是否已解决冲突，并且增加到暂存区?”，
 *  3. 目前的merge是全量的，不是单个文件merge, 如果单个文件建议查看，逻辑介绍第2点。
 *  4. isAutoSlamFePackage是表示是否在完成merge后，自动打包到ark-fe下的ark-build
 *
 *  三、配置JSON，说明
 *  {
 *  // target 表示要合并的目标分支
    "target": [{
        "source": "ark-fe-dev-1.5.0-autoPackage", // 目标分支
        "packagePath": "ark-fe-dev-1.5.0-autoPackage"// 目标打包分支名称 // 如果不配置，则默认和source相同；
    }, {
        "source": "ark-fe-dev-1.5.0-1440",
        "packagePath": "ark-fe-dev-1.5.0-1440"
    }],
        // 分支描述，后期会扩展group
        "branch": [{
            "source": "ark-fe-dev",
            "descirbe": "基础稳定分支",
        },
        {
            "source": "ark-fe-dev-xxx",
            "descirbe": "基础稳定分支的功能分支"
        },
        {
            "source": "ark-fe-test",
            "descirbe": "功能分支的稳定分支"
        },
        {
            "source": "ark-fe-rc-1.5.0.0",
            "descirbe": "南京工厂的回归分支",
        },
        {
            "source": "ark-fe-rc-1.6.0.0",
            "descirbe": "柯尼卡的回归分支"
        }
    ],
    // 表示ark-fe需不需要打包，如果不需要打包给false,
    "isAutoSlamFePackage": false,
  }
   四、未来会增加内容：
    1. group的概念
    2. 单个文件更新到所有分支；
    3. 设计不合理，后续会改成class
    4. 自身分支merge，其他分支，在提交
 */
const exec = require('child_process').exec;
const fork = require('child_process').fork;
const prompt = require('inquirer').prompt;
const config = require('./config.js');
const path = require('path');

// 全部统一到根目录
const baseUrl =
  path.basename(process.cwd()) === 'build_merge_tools' ? '../' : './';

const { target, isAutoSlamFePackage } = config;

// 自定义console输出
const mConsole = {
  warn(msg) {
    console.log(`\x1b[43;30m  警告 \x1b[40;33m ${msg} \x1b[0m`);
  },
  success(msg) {
    console.log(`\x1b[42;30m 成功 \x1b[40;32m ${msg} \x1b[0m`);
  },
  error(msg) {
    console.log(`\x1b[41;30m 失败 \x1b[40;31m ${msg} \x1b[0m`);
  },
};

// 获取所有的暂存区文件
const getAllBlockCode = () =>
  new Promise((resolve) => {
    const hasStagedFiles = exec('git diff --name-status --staged');
    let stdOut = '';
    hasStagedFiles.stdout.on('data', (data) => {
      if (data && data.length) {
        mConsole.success('> 当前分支');
        console.log(data);
      }
      stdOut = data;
    });
    hasStagedFiles.stderr.on('data', (data) => {
      if (data && data.length) {
        mConsole.error('> 获取暂存区失败');
        console.log(data);
      }
    });
    hasStagedFiles.on('close', (code) => {
      if (!Number(code)) {
        resolve(stdOut);
      }
    });
  });

// 提交所有的暂存区/push到远程
const commitAllCode = branch =>
  new Promise((resolve) => {
    prompt({
      type: 'input',
      name: 'commitMessage',
      message: '请输入commit的信息！',
    }).then((answers) => {
      const { commitMessage } = answers;
      exec(
        `git commit -m ${commitMessage} && git push  origin ${branch}`,
        (err, stdout, stderr) => {
          if (!err) {
            setTimeout(() => {
              mConsole.success('> push到远程了');
              console.log(stdout);
              resolve();
            }, 100);
          } else {
            mConsole.error('> push到远程失败');
            console.log(stderr);
          }
        },
      );
    });
  });

const pullFreshCode = branch =>
  new Promise((resolve) => {
    exec(`git pull origin ${branch}`, (error, stdout, stderr) => {
      if (!error) {
        mConsole.success(`> 分支origin/${branch}：pull成功`);
        resolve();
      } else {
        mConsole.error(`> 分支origin/${branch}：pull失败`);
        console.log(stderr);
      }
    });
  });

// 切换分支
const checkOutToSource = item =>
  new Promise((resolve) => {
    exec(
      `git checkout -b ${item.source} --track origin/${item.source}`,
      (error, stdout, stderr) => {
        if (error) {
          exec(`git checkout ${item.source}`, (err, out, errmessage) => {
            if (err) {
              mConsole.error(`> checkout到${item.source}失败`);
              console.log(errmessage);
            } else {
              pullFreshCode(item.source).then(() => resolve());
            }
          });
        } else {
          pullFreshCode(item.source).then(() => resolve());
        }
      },
    );
  });

// 检查是否有冲突；
/**
 *  先判断是否有冲突文件
 *  如果有，触发交互询问，用户输入处理结果后，在执行一次检查，如果还出现，继续显示交互询问
 *  如果没有了， 检查是否存在暂存区， 如果不存在，直接push， 如果存在commit 在push
 */
const checkHasConflict = (resolve, source) => {
  const confirmMessage = () =>
    prompt([
      {
        type: 'confirm',
        name: 'test',
        message: '> 是否已解决冲突，并且增加到暂存区?',
        default: false,
      },
    ]);
  mConsole.warn('> 检查当前是否有合并冲突文件...');
  // 因为git diff 存在无输出的情况，所以改用监听close的方式；
  const hasConfilctFiles = exec('git diff --name-only --diff-filter=U');
  let stdout = '';
  let stderrMessage = '';
  hasConfilctFiles.stdout.on('data', (data) => {
    stdout = data;
  });
  hasConfilctFiles.stderr.on('data', (data) => {
    stderrMessage = data;
  });
  hasConfilctFiles.on('close', (code) => {
    if (!code) {
      if (stdout && stdout.length > 0) {
        // 存在冲突文件
        mConsole.warn('> 当前冲突文件列表：');
        console.log(stdout);
        confirmMessage().then(() => {
          checkHasConflict(resolve, source);
        });
      } else {
        // 不存在冲突文件
        getAllBlockCode().then((fileList) => {
          if (fileList && fileList.length > 0) {
            commitAllCode(source).then(() => resolve());
          } else {
            exec(
              `git push origin ${source}`,
              (pushError, pushStdout, pushErr) => {
                if (!pushError) {
                  setTimeout(() => {
                    mConsole.success('> push到远程');
                    console.log(pushStdout);
                    resolve();
                  }, 100);
                } else {
                  mConsole.error('> push到远程失败');
                  console.log(pushErr);
                }
              },
            );
          }
        });
      }
    } else {
      mConsole.error('> 检查冲突失败');
      console.log(stderrMessage);
    }
  });
};
// 合并分支
const mergeBranches = (branches, source) =>
  new Promise((resolve) => {
    exec(
      `git pull && git merge  origin ${branches}`,
      (error, stdout, stderr) => {
        mConsole.success(`> 已合并${branches}分支`);
        checkHasConflict(resolve, source);
      },
    );
  });

// 每次切换到新分支执行的动作
const checkoutNewBranche = async (index, branch, total) => {
  const object = total[index];
  if (index > 0) console.log('\n\n');
  if (object) {
    mConsole.warn(`==== 正在执行${object.source}分支的相关操作 ===`);
    // 切换到新分支
    await checkOutToSource(object);
    // 拉取最新的
    await mergeBranches(branch, object.source);
    // 打包合并代码
    if (isAutoSlamFePackage) {
      const n = fork(path.resolve(baseUrl, 'build_tools/nBuild.js'));
      mConsole.warn(`开始执行${object.source}分支打包到ark-fe-build下`);
      n.on('message', ({ type }) => {
        // 执行成功
        if (!Number(type)) {
          mConsole.success('打包成功，并且上传成功');
          checkoutNewBranche(Number(index) + 1, branch, total);
        } else {
          mConsole.error('打包失败');
        }
      });
      n.send(object);
    } else {
      // 检查是否执行下一个分支
      checkoutNewBranche(index + 1, branch, total);
    }
    return true;
  }
  process.exit();
  return false;
};

/**
 *  组织逻辑代码
 */
const main = async (branch) => {
  const fileList = await getAllBlockCode();
  if (fileList && fileList.length > 0) {
    await pullFreshCode(branch);
    await commitAllCode(branch);
  }
  mConsole.success('> 准备切换到目标分支，进行合并操作');
  checkoutNewBranche(0, branch, target);
};

exec('git symbolic-ref --short -q HEAD', (err, stdout, stderr) => {
  if (!err) {
    main(stdout);
  }
});
