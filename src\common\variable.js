/**
 
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

            此文件将被弃用, 常量在 src/common/constants.js 文件中维护

!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!









 * 备注：存放系统常量
 *
 * 常量必须全大写
 *
 * */
// 辊筒机器人
export const ROLL_ROBOT = "2";
// 机器人种类新增常量 2020-08-24 by luoyaodong
// 牵引
export const TOW = "0";
// 举升
export const LIFT = "1";
// 新增叉车常量 2020-10-29 luoyaodong
// 叉车
export const FORKLIFT = "4";
// 复合机器人
export const MULTIROBOT = "8";
// 到达后操作动作对应的value  -> 用户系统内常量判断
export const arriveOperation_dict = {
  liftUp: "1", // 举升
  putDown: "2", // 放下
  goShift: "13", // 偏移
  goTurnOfSide: "15", // 旋转至
  goTurnOfAngle: "14", // 旋转角度
  fetchStuff: "11", // 取货
  putDownStuff: "12", // 放货
  goTurnOfNoumenonAngle: "20", // 本体按角度旋转
};

// 触发时机逻辑字典
export const triggerHandler_dict = {
  // 5-6-7 表示业主规则流转 的 触发时机。
  lastTaskArrived: "5",
  initiateNextTask: "6",
  end: "7",
  // 非 5-6-7 表示机器人任务流转规则
  timeTrigger: "1",
  // 手动触发自动判断分支
  choiceByUser: "2",
  // 手动选择分支
  manually: "4",
};

// cellType字典
export const cellType_dict = {
  flowNode: "46", // 子流程节点 nodeType+cellType构造的
  endNode: "0", // 结束节点
  autoNode: "11", // 动态模板 -> 自动赋值节点
  startNode: "1", // 开始节点
  interfaceNode: "8", // 接口赋值节点
};

// 流程画布 -> 线类型判断
export const edgeType_dict = {
  edgeRule: "1", // 表示业务流程线
  edgeTask: "0", // 表示机器人任务流程线
};

// 当前节点状态
export const nodeStatus_dict = {
  full: "1",
  empty: "2",
};

// 异常处理
export const exceptionHandler_dict = {
  taskQueue: "1",
  idlePriority: "2",
  failure: "0",
  platformPriority: "3",
  cacheQueue: "4",
  robotQueue: "5",
};

// cellType
export const batch_cellType_dict = {
  virtualNode: "7",
  station: "2",
  area: "4",
  generalNode: "10",
  palletCode: "100",
  equipment: "12", // 设备节点
};

export const flowTemplate_dict = {
  point: "10",
  multiPoint: "20",
  dynamicPoint: "30",
  multipointCustom: "40",
};

// 设备归属
export const deviceOwnType_dict = {
  externalDevice: "1",
  robotDeviceComponent: "2",
};
// 上料点管理 - 送料目的点
export const materialDestinationSelectType_dict = {
  manul: "2",
  auto: "1",
};

// 容器形态枚举
export const containerModel_dict = {
  tray: "1", // 托盘
  rack: "2", // 货架
  forkliftTray: "3", // 叉车托盘
  poleCabinet: "4", // 带杆料柜
};

// 控制逻辑
export const logicControl_dict = {
  previousNode: "1",
  nextPoint: "2",
};

// 等待指令
export const waitCodeType_dict = {
  breakAndWait: "1", // 中断等待
  manualConfig: "2", // 手动配置
};

// 移除容器
export const canDeleteshelfFlag_dict = {
  yes: "1",
  no: "0",
};

// 机器人原地等待
export const robotWaitFlag_dict = {
  yes: "1",
  no: "0",
};

// 复合指令控制显示
export const robotCommand_dict = {
  robotCommandPlay: "MEDIA_PLAY", // 语音播放
  robotCommandPlayStop: "MEDIA_STOP", // 语音播放停止
};

// 功能组件
export const pluginComponent_dict = {
  emptyContainerReturn: "9",
};
