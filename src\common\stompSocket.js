/**
 * Stomp WebSocket通信基础
 */
import Stomp from "stompjs";
import config from "@/common/config";
import { MSG_RESPONSE, SEND_MESSAGE } from "@/constants/stationWsMsg";
import { t } from "@/libs_sz/locale";
import eventEmitter from "./eventEmitter";

export default {
  name: "entry",
  data() {
    return {
      client: "",
      msg: [],
      text: "",
    };
  },
  created() {
    this.registerWSOpenEvent();
    this.registerWScloseEvent();
    this.registerWSSendEvent();
    this.registerWSSubcribeEvent();
    this.registerWSStopEvent();
  },
  beforeDestroy() {
    //= = 后台强烈要求，断开连接要发送一个disconnected
    eventEmitter.emit("stationStop");
    eventEmitter.removeAllListeners("stationOpen");
    eventEmitter.removeAllListeners("stationClose");
    eventEmitter.removeAllListeners("stationSend");
    eventEmitter.removeAllListeners("stationSubcribe");
  },

  methods: {
    onConnected() {
      if (this.loading) this.loading.close();
      this.$message({
        message: `${t("lang.ark.fed.successfulConnectionToTheServer")}!`,
        type: "success",
      });

      const options = {
        command: MSG_RESPONSE,
        headers: {},
        callback: this.responseCallback,
      };
      // ---订阅频道
      eventEmitter.emit("stationSubcribe", options);
      // 链接成功回调方法
      this.connectedCallback && this.connectedCallback();
    },
    onFailed(frame) {
      this.restart(frame);
    },
    responseCallback() {},
    restart() {
      this.loading = this.$loading({
        lock: true,
        text: t("lang.ark.fed.theConnectionIsClosedAndReconnectedToTheServer"),
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
      });

      setTimeout(() => {
        this.connect();
      }, 1000);
    },
    /**
     *  sign 标志 true 表示重连以后的状态， 默认false，表示正常状态；
     */
    connect(sign) {
      this.client = Stomp.client(config.STATION_SERVICE);
      // ---初始化mqtt客户端，并连接mqtt服务
      const headers = { connnect: 123 };
      this.client.connect(headers, this.onConnected, this.onFailed);
      // client will send heartbeats every 20000ms
      this.client.heartbeat.outgoing = 20000;
      this.client.heartbeat.incoming = 0;
      sign && window.location.reload();
    },
    registerWSOpenEvent() {
      eventEmitter.on("stationOpen", () => {
        this.connect();
      });
    },
    registerWScloseEvent() {
      eventEmitter.on("stationClose", (frame) => {
        this.restart(frame);
      });
    },
    registerWSSendEvent() {
      eventEmitter.on("stationSend", (data) => {
        let sendObj = {
          command: SEND_MESSAGE,
          headers: {},
          body: "",
        };
        sendObj = { ...sendObj, ...data };
        if (this.client) {
          // 发送消息
          this.client.send(sendObj.command, sendObj.headers, JSON.stringify(sendObj.body));
        }
      });
    },
    registerWSSubcribeEvent() {
      eventEmitter.on("stationSubcribe", (data) => {
        let sendObj = {
          command: "",
          headers: {},
          callback: "",
        };
        sendObj = { ...data };
        // 发送消息
        this.client.subscribe(sendObj.command, sendObj.callback, JSON.stringify(sendObj.headers));
      });
    },
    registerWSStopEvent() {
      eventEmitter.on("stationStop", () => {
        this.client.disconnect(() => {});
      });
    },
  },
};
