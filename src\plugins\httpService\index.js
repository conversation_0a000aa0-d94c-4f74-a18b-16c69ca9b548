import Vue from "vue";
import apiStore from "@/common/apiStore";
import Promise from "es6-promise";
import httpService from "@/libs_sz/plugins/httpService";
import { Message } from "element-ui";
import { linkAuthMange, noPermission } from "@/utils/getSessionId";
import myConfig from "@/common/config";
import { throttle } from "lodash";

Promise.polyfill();

Vue.use(httpService, {
  // 默认错误
  messageFn: throttle(function (type, msg) {
    const $vm = window.vm;
    if (type !== "error") return;
    if ($vm && $vm.$isMobile) {
      $vm.$vux.toast.text(msg, "middle");
    } else {
      Message.error(msg);
    }
  }, 4000),
  requestInterceptor(config) {
    const nconfig = config;
    nconfig.headers.common["Accept-Language"] = window.vm.$getLang();
    nconfig.headers.common["Gek-Authorization"] = localStorage.getItem("Gek-Authorization") || "";
    return nconfig;
  },
  apiStore,
  authentication: true, // 是否需要开启鉴权；
  // 开启鉴权后的默认处理，必填;
  authenticationResponse({ code }, next) {
    if (String(code) === "2") {
      // Message.error(`${window.vm.$t("lang.ark.fed.leavingSoon")}...`);
      setTimeout(linkAuthMange, 200);
    } else if (String(code) === "3") {
      noPermission();
    }
    next();
  },
});

// 增加axios的默认配置项baseUrl
httpService.gAxios.changeAxiosAttribute({ baseURL: myConfig.ROOT });
