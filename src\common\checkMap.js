import * as constants from "@/constants/commonConstants";
import { getMap } from "@/common/common";
import { mapGetters, mapActions } from "vuex";
import * as types from "@/store/mutationTypes";
import config from "@/common/config";

export default {
  data() {
    return {};
  },
  computed: {
    ...mapGetters(["mapId", "mapUrl"]),
  },
  methods: {
    reqMap() {
      const that = this;
      const timeStamp = new Date().getTime();
      getMap({ mapId: 0 }).then((result) => {
        this.$store.commit(types.IS_LOADING, false);
        if (result.code === 0) {
          const reqData = result.data ? result.data : result;
          that.getMapUrl(reqData.mapUrl);
          this.getMapId(reqData.mapId);
          this.mapData = reqData;
          localStorage.geekMapUrl = `${config.marsRoot}${reqData.mapUrl}?t=${timeStamp}`;
          localStorage.geekMapId = reqData.mapId;
          localStorage.geekMapName = reqData.name;
          localStorage.geekMapWidth = reqData.width;
          localStorage.geekMapHeight = reqData.height;
          localStorage.geekMapResolution = reqData.resolution;
          localStorage.geekMapOrigX = reqData.x;
          localStorage.geekMapOrigY = reqData.y;
          localStorage.geekMapYaw = reqData.yaw;
        } else {
          // 如果没有地图，进入地图页，强制回到绘制地图页；
          this.getMapId(null);
          this.$router.push("/start");
        }
      });
    },
    ...mapActions(["getMapId", "getMapUrl"]),
  },
};
