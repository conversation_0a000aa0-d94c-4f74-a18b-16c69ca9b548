import { t } from "@/libs_sz/locale";
import myTransform from "@/libs_sz/utils/transform";

/**
 * 用来返回element ui中 from 可用的 rule 数据
 * 返回始终是个数组
 *
 * 使用方法:
 * 1. rule["required"]  必填项
 * 2. rule["input8"]  限定只能输入8位
 * 3. rule["input4:8"]  限定只能输入4 ~ 8位
 * 4. rule["input:8"]  限定只能输入0 ~ 8位
 *
 * 如果要使用多个匹配, 可用,隔开
 * 5. rule["required, input:8"]  必填且限定只能输入0 ~ 8位
 */

// 因为需要动态的获取国际化, 所以需要返回一个函数
const ruleMap = {
  required() {
    return {
      required: true,
      message: t("libsSz.key35"),
    };
  },
  ipRules() {
    return {
      pattern: /((?:(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d))\.){3}(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d)))/,
      message: t("libsSz.key37"),
    };
  },
  //子网掩码
  maskRules() {
    return {
      pattern: /((?:(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d))\.){3}(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d)))/,
      message: t("libsSz.key67"),
    };
  },
  //网关地址
  gateWayRules() {
    return {
      pattern: /((?:(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d))\.){3}(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d)))/,
      message: t("libsSz.key68"),
    };
  },
  portRules() {
    return {
      pattern: /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
      message: t("libsSz.key66"),
    };
  },
  intRules() {
    return {
      pattern: /^[0-9]\d{0,}$/,
      message: t("libsSz.key43"),
    };
  },
  positiveInt() {
    return {
      pattern: /^[1-9]\d{0,}$/,
      message: t("libsSz.key38"),
    };
  },
  telephone() {
    return {
      pattern: /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/,
      message: t("libsSz.key44"),
    };
  },
  specialCharacters() {
    return {
      pattern: /^(a-z|A-Z|0-9)*[^~!@#$%^&*()']*$/,
      message: t("libsSz.key34"),
    };
  },
  numbersOrWords() {
    return {
      pattern: /^[0-9a-zA-Z]*$/,
      message: t("libsSz.key65"),
    };
  },
  numbers() {
    return {
      pattern: /^[+]{0,1}(\d+)$/,
      message: t("libsSz.key67"),
    };
  },
  supportNumbers() {
    return {
      pattern: /^(-|\+)?\d+$/,
      message: t("libsSz.key94"),
    };
  },
};

// ===== 高阶
// 需要使用正则动态返回的rule, 优先级高于ruleMap中的内容
// 从数组的第一位开始做正则的匹配, 如果多个正则都匹配成功, 只取第一个
const ruleGenerateMap = [
  {
    // 例: inputLen10 表示只匹配长度为 10 的文本
    // 仅支持正整数
    exp: /^input(\d+)$/,
    output(key, expResult) {
      const input = Number(expResult[1]);
      return {
        min: input,
        max: input,
        message: t("libsSz.key46", [input]),
      };
    },
  },

  {
    // 例: inputLen5:10 表示匹配 5 ~ 10 位的文本
    // 其中 min可以省略, 如: inputLen:10 表示匹配 0 ~ 10 位的文本
    // 仅支持正整数
    exp: /^input(\d?):(\d+)$/,
    output(key, expResult) {
      const min = Number(expResult[1] || 0);
      const max = Number(expResult[2]);
      return {
        min,
        max,
        message: t("libsSz.key36", [max]),
      };
    },
  },
];

export const rule = new Proxy(ruleMap, {
  get(target, key) {
    const len = ruleGenerateMap.length;
    const keyList = key.split(",");
    return keyList.map((keyItem) => {
      // 正则匹配
      for (let i = 0; i < len; i += 1) {
        const ruleItem = ruleGenerateMap[i];
        const ruleExpResult = ruleItem.exp.exec(keyItem.trim());
        if (ruleExpResult) {
          return ruleItem.output(key, ruleExpResult);
        }
      }
      return target[keyItem.trim()]();
    });
  },
  set() {},
});

export function generateUUID() {
  let d = new Date().getTime();
  if (window.performance && typeof window.performance.now === "function") {
    d += performance.now(); //use high-precision timer if available
  }
  let uuid = "xxxxxxxx".replace(/[xy]/g, function (c) {
    let r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
}

export const getFormatter = (data, value) => {
  const getValue = myTransform.arrToObject(data, "value", "label")[value] || value;
  return getValue === "null" ? "" : getValue;
};
