;/*++
;
;Module Name:
;
;    SLABVCP.INF
;
;    Copyright 2013-2016, Silicon Laboratories Inc.
;
;Abstract:
;    Installation INF for Silicon Labs CP210x device
;
;--*/

[Version]
Signature="$WINDOWS NT$"
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%Provider%
DriverVer=09/19/2016,6.7.4.261
CatalogFile=slabvcp.cat
PnpLockDown=1	; "a driver package should set PnpLockDown to 1" -- MSDN

; ================= Device section =====================

[Manufacturer]
%ManufacturerName%=SiLabsModelsSection, NTx86.6.1, NTamd64.6.1, NTarm.10, NTarm64.10

;Models section for installation of x86 driver on Windows 7 and above
[SiLabsModelsSection.NTx86.6.1]
%USB\VID_10C4&PID_EA60.DeviceDesc%      =SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA60		; USB\VID_v(4)&PID_d(4)
%USB\VID_10C4&PID_EA63.DeviceDesc%      =SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA63
%USB\VID_10C4&PID_EA70&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA70&Mi_00	; USB\VID_v(4)&PID_d(4)&MI_z(2)
%USB\VID_10C4&PID_EA70&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA70&Mi_01
%USB\VID_10C4&PID_EA71&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA71&Mi_00
%USB\VID_10C4&PID_EA71&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA71&Mi_01
%USB\VID_10C4&PID_EA71&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA71&Mi_02
%USB\VID_10C4&PID_EA71&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA71&Mi_03
%USB\VID_10C4&PID_EA7A&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7A&Mi_00
%USB\VID_10C4&PID_EA7A&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7A&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7B&Mi_00
%USB\VID_10C4&PID_EA7B&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7B&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7B&Mi_02
%USB\VID_10C4&PID_EA7B&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTx86, USB\VID_10C4&PID_EA7B&Mi_03

;Models section for installation of x64 driver on Windows 7 and above
[SiLabsModelsSection.NTamd64.6.1]
%USB\VID_10C4&PID_EA60.DeviceDesc%      =SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA60			; USB\VID_v(4)&PID_d(4)
%USB\VID_10C4&PID_EA63.DeviceDesc%      =SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA63
%USB\VID_10C4&PID_EA70&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA70&Mi_00	; USB\VID_v(4)&PID_d(4)&MI_z(2)
%USB\VID_10C4&PID_EA70&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA70&Mi_01
%USB\VID_10C4&PID_EA71&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA71&Mi_00
%USB\VID_10C4&PID_EA71&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA71&Mi_01
%USB\VID_10C4&PID_EA71&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA71&Mi_02
%USB\VID_10C4&PID_EA71&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA71&Mi_03
%USB\VID_10C4&PID_EA7A&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7A&Mi_00
%USB\VID_10C4&PID_EA7A&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7A&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7B&Mi_00
%USB\VID_10C4&PID_EA7B&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7B&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7B&Mi_02
%USB\VID_10C4&PID_EA7B&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTamd64, USB\VID_10C4&PID_EA7B&Mi_03

;Models section for installation of arm driver on Windows 10 and above
[SiLabsModelsSection.NTarm.10]
%USB\VID_10C4&PID_EA60.DeviceDesc%      =SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA60		; USB\VID_v(4)&PID_d(4)
%USB\VID_10C4&PID_EA63.DeviceDesc%      =SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA63
%USB\VID_10C4&PID_EA70&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA70&Mi_00	; USB\VID_v(4)&PID_d(4)&MI_z(2)
%USB\VID_10C4&PID_EA70&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA70&Mi_01
%USB\VID_10C4&PID_EA71&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA71&Mi_00
%USB\VID_10C4&PID_EA71&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA71&Mi_01
%USB\VID_10C4&PID_EA71&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA71&Mi_02
%USB\VID_10C4&PID_EA71&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA71&Mi_03
%USB\VID_10C4&PID_EA7A&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7A&Mi_00
%USB\VID_10C4&PID_EA7A&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7A&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7B&Mi_00
%USB\VID_10C4&PID_EA7B&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7B&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7B&Mi_02
%USB\VID_10C4&PID_EA7B&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTarm, USB\VID_10C4&PID_EA7B&Mi_03

;Models section for installation of arm64 driver on Windows 10 and above
[SiLabsModelsSection.NTarm64.10]
%USB\VID_10C4&PID_EA60.DeviceDesc%      =SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA60			; USB\VID_v(4)&PID_d(4)
%USB\VID_10C4&PID_EA63.DeviceDesc%      =SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA63
%USB\VID_10C4&PID_EA70&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA70&Mi_00	; USB\VID_v(4)&PID_d(4)&MI_z(2)
%USB\VID_10C4&PID_EA70&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA70&Mi_01
%USB\VID_10C4&PID_EA71&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA71&Mi_00
%USB\VID_10C4&PID_EA71&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA71&Mi_01
%USB\VID_10C4&PID_EA71&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA71&Mi_02
%USB\VID_10C4&PID_EA71&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA71&Mi_03
%USB\VID_10C4&PID_EA7A&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7A&Mi_00
%USB\VID_10C4&PID_EA7A&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7A&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_00.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7B&Mi_00
%USB\VID_10C4&PID_EA7B&Mi_01.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7B&Mi_01
%USB\VID_10C4&PID_EA7B&Mi_02.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7B&Mi_02
%USB\VID_10C4&PID_EA7B&Mi_03.DeviceDesc%=SiLabsDDInstallSection.NTarm64, USB\VID_10C4&PID_EA7B&Mi_03

;DDInstall sections (one per x86, amd64, arm, arm64)
; Note: If/as we are building a Universal driver package, we can not use a DefaultInstall section.
[SiLabsDDInstallSection.NTx86]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40	; a single-byte hexadecimal number between 0x00 and 0xFF, A lower featurescore value specifies a better feature score rank, where 0x00 is the best feature score rank.

[SiLabsDDInstallSection.NTamd64]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40	; a single-byte hexadecimal number between 0x00 and 0xFF, A lower featurescore value specifies a better feature score rank, where 0x00 is the best feature score rank.

[SiLabsDDInstallSection.NTarm]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40	; a single-byte hexadecimal number between 0x00 and 0xFF, A lower featurescore value specifies a better feature score rank, where 0x00 is the best feature score rank.

[SiLabsDDInstallSection.NTarm64]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40	; a single-byte hexadecimal number between 0x00 and 0xFF, A lower featurescore value specifies a better feature score rank, where 0x00 is the best feature score rank.


;DDInstall.Services sections (one per x86, amd64, arm, arm64)
; Note: If/as we are building a Universal driver package, we can not use a DefaultInstall.Services section.
[SiLabsDDInstallSection.NTx86.Services]
AddService = silabser,0x00000002,silabser.AddService

[SiLabsDDInstallSection.NTamd64.Services]
AddService = silabser,0x00000002,silabser.AddService

[SiLabsDDInstallSection.NTarm.Services]
AddService = silabser,0x00000002,silabser.AddService

[SiLabsDDInstallSection.NTarm64.Services]
AddService = silabser,0x00000002,silabser.AddService

[silabser.AddService]
DisplayName    = %silabser.SvcDesc%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\silabser.sys

; common registry entries 
[silabser.AddReg]
HKR,,NTMPDriver,,silabser.sys
HKR,,RateLimitPurgeMS, 0x10001, 0x64, 0x00, 0x00, 0x00
HKR,,OverrideDefaultPortSettings, 0x10001, 01,00,00,00
HKR,,InitialBaudRate, 0x10001, 00,C2,01,00		;115200 initial baud rate
HKR,,InitialLineControl,, "8N1"				;8-bits, No parity, 1 stop bit
HKR,,PortSubClass,1,01
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

;DDInstall.HW sections (one per x86, amd64, arm, arm64)
[SiLabsDDInstallSection.NTx86.HW]
AddReg=SiLabsDDInstallSection.HW.AddReg

[SiLabsDDInstallSection.NTamd64.HW]
AddReg=SiLabsDDInstallSection.HW.AddReg

[SiLabsDDInstallSection.NTarm.HW]
AddReg=SiLabsDDInstallSection.HW.AddReg

[SiLabsDDInstallSection.NTarm64.HW]
AddReg=SiLabsDDInstallSection.HW.AddReg

[SiLabsDDInstallSection.HW.AddReg]
HKR,,"SelectiveSuspendTimeout",0x00010001,10000
HKR,,"DisableHwAccessInModemStatusIoctls",0x00010001,1
; Attention! The EnablePowerManagewment value is no longer supported.
; To disable Selective Suspend, uncomment the following line:
; HKR,,"DisableS0Idle",0x00010001,1

[silabser.Files.Ext]
silabser.sys

[SourceDisksNames.x86]
1=%Disk_Description%,"slabvcp.cat"
[SourceDisksNames.amd64]
1=%Disk_Description%,"slabvcp.cat"
[SourceDisksNames.arm]
1=%Disk_Description%,"slabvcp.cat"
[SourceDisksNames.arm64]
1=%Disk_Description%,"slabvcp.cat"

[SourceDisksFiles.x86]
silabser.sys = 1,x86
WdfCoinstaller01009.dll=1,x86

[SourceDisksFiles.amd64]
silabser.sys = 1,x64
WdfCoinstaller01009.dll=1,x64

[SourceDisksFiles.arm]
silabser.sys = 1,arm
WdfCoinstaller01011.dll=1,arm

[SourceDisksFiles.arm64]
silabser.sys = 1,arm64
WdfCoinstaller01015.dll=1,arm64

[DestinationDirs]
Silabser.Files.Ext = 12 ; windows\system32\drivers

;-------------- WDF Coinstaller installation
[DestinationDirs]
CoInstaller_CopyFiles.KMDF.1.09 = 11 ; windows\system32
CoInstaller_CopyFiles.KMDF.1.11 = 11 ; windows\system32
CoInstaller_CopyFiles.KMDF.1.15 = 11 ; windows\system32

;DDInstall.CoInstallers sections (one per x86, amd64, arm, arm64)
; "You can use any INF section in a universal INF file except for [CoInstallers]" -- MSDN
[SiLabsDDInstallSection.NTx86.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.09
CopyFiles=CoInstaller_CopyFiles.KMDF.1.09

[SiLabsDDInstallSection.NTamd64.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.09
CopyFiles=CoInstaller_CopyFiles.KMDF.1.09

[SiLabsDDInstallSection.NTarm.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.11
CopyFiles=CoInstaller_CopyFiles.KMDF.1.11

[SiLabsDDInstallSection.NTarm64.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.15
CopyFiles=CoInstaller_CopyFiles.KMDF.1.15

[CoInstaller_CopyFiles.KMDF.1.09]
WdfCoinstaller01009.dll
[CoInstaller_CopyFiles.KMDF.1.11]
WdfCoinstaller01011.dll
[CoInstaller_CopyFiles.KMDF.1.15]
WdfCoinstaller01015.dll

[SourceDisksFiles]
WdfCoinstaller01009.dll=1
WdfCoinstaller01011.dll=1
WdfCoinstaller01015.dll=1

[CoInstaller_AddReg.KMDF.1.09]
HKR,,CoInstallers32,0x00010000, "WdfCoinstaller01009.dll,WdfCoInstaller"
[CoInstaller_AddReg.KMDF.1.11]
HKR,,CoInstallers32,0x00010000, "WdfCoinstaller01011.dll,WdfCoInstaller"
[CoInstaller_AddReg.KMDF.1.15]
HKR,,CoInstallers32,0x00010000, "WdfCoinstaller01015.dll,WdfCoInstaller"


;DDInstall.Wdf sections (one per x86, amd64, arm, arm64)
[SiLabsDDInstallSection.NTx86.Wdf]
KmdfService = silabser, SiLabs_wdfsect.1.09

[SiLabsDDInstallSection.NTamd64.Wdf]
KmdfService = silabser, SiLabs_wdfsect.1.09

[SiLabsDDInstallSection.NTarm.Wdf]
KmdfService = silabser, SiLabs_wdfsect.1.11

[SiLabsDDInstallSection.NTarm64.Wdf]
KmdfService = silabser, SiLabs_wdfsect.1.15

[SiLabs_wdfsect.1.09]
KmdfLibraryVersion = 1.09
[SiLabs_wdfsect.1.11]
KmdfLibraryVersion = 1.11
[SiLabs_wdfsect.1.15]
KmdfLibraryVersion = 1.15

;---------------------------------------------------------------;

[Strings]
Provider="Silicon Laboratories Inc."
ManufacturerName="Silicon Labs"
Disk_Description=                      "Silicon Labs CP210x USB to UART Bridge Installation Disk"
USB\VID_10C4&PID_EA60.DeviceDesc=      "Silicon Labs CP210x USB to UART Bridge"
USB\VID_10C4&PID_EA63.DeviceDesc=      "Silicon Labs CP210x USB to UART Bridge"
USB\VID_10C4&PID_EA70&Mi_00.DeviceDesc="Silicon Labs Dual CP2105 USB to UART Bridge: Enhanced COM Port"
USB\VID_10C4&PID_EA70&Mi_01.DeviceDesc="Silicon Labs Dual CP2105 USB to UART Bridge: Standard COM Port"
USB\VID_10C4&PID_EA71&Mi_00.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 0"
USB\VID_10C4&PID_EA71&Mi_01.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 1"
USB\VID_10C4&PID_EA71&Mi_02.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 2"
USB\VID_10C4&PID_EA71&Mi_03.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 3"
USB\VID_10C4&PID_EA7A&Mi_00.DeviceDesc="Silicon Labs Dual CP2105 USB to UART Bridge: Enhanced COM Port"
USB\VID_10C4&PID_EA7A&Mi_01.DeviceDesc="Silicon Labs Dual CP2105 USB to UART Bridge: Standard COM Port"
USB\VID_10C4&PID_EA7B&Mi_00.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 0"
USB\VID_10C4&PID_EA7B&Mi_01.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 1"
USB\VID_10C4&PID_EA7B&Mi_02.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 2"
USB\VID_10C4&PID_EA7B&Mi_03.DeviceDesc="Silicon Labs Quad CP2108 USB to UART Bridge: Interface 3"
silabser.SvcDesc="Silicon Labs CP210x USB to UART Bridge Driver"
