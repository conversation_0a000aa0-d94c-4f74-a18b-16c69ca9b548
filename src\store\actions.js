import * as types from "@/store/mutationTypes";
import { recursion } from "@/utils/commonTranform";
import myTransform from "@/libs_sz/utils/transform";
import apiStore from "@/common/apiStore";
import { GET_MODULE_MENUS, IS_LOGIN_STATION } from "@/constants/api";
import myConfig from "@/common/config";
import { linkAuthMange } from "@/utils/getSessionId";
import axios from "axios";
import { httpService } from "@/libs_sz/plugins/httpService";

/** 获取场地 */
export const getStore = ({ commit }, data) => {
  commit(types.GET_STORE, data);
};
/** 获取机器人状态 */
export const getRobotStatus = ({ commit }, data) => {
  commit(types.GET_ROBOT_STATUS, data);
};
/** 获取地图对应机器人 */
export const getMapRobot = ({ commit }, data) => {
  commit(types.GET_MAP_ROBOT, data);
};
/** 设置 场地索引 */
export const setStoreIndex = ({ commit }, index) => {
  commit(types.SET_STORE_INDEX, index);
};

/** 获取 所有机器人状态 */
export const getRobotStatusAll = ({ commit }, data) => {
  commit(types.GET_ROBOT_STATUS_ALL, data);
};

/** 获取 地图 解决方案 */
export const mapGetResolution = ({ commit }, resolution) => {
  commit(types.GET_RESOLUTION, resolution);
};
/** 获取 任务 列表 */
export const getTaskList = ({ commit }, data) => {
  commit(types.GET_TASK_LIST, data);
};

/** 获取 设施 列表 */
export const getFacilityList = ({ commit }, data) => {
  commit(types.GET_FACILITY_LIST, data);
};
/** 获取 道路 列表 */
export const getPathList = ({ commit }, data) => {
  commit(types.GET_PATH_LIST, data);
};
/** 获取 逻辑去 列表 */
export const getLogicList = ({ commit }, data) => {
  const logicData = [];
  for (let i = 0; i < data.length; i += 1) {
    const item = data[i];
    item.type = 901;
    logicData.push(item);
  }
  commit(types.GET_LOGIC_LIST, logicData);
};

/** 获取 地图 列表 */
export const getMapUrl = ({ commit }, url) => {
  commit(types.GET_MAP_URL, url);
};

/** 获取 地图 列表 */
export const getMapId = ({ commit }, id) => {
  commit(types.GET_MAP_ID, id);
};

/** 获取 任务 列表 */
export const getTaskEditData = ({ commit }, taskData) => {
  commit(types.GET_TASK_EDIT_DATA, taskData);
};

export const showRobot = ({ commit }) => {
  commit(types.SHOW_ROBOT);
};

export const showPoint = ({ commit }) => {
  commit(types.SHOW_ROBOT);
};

/** 获取 工作站 列表 */
export const getStationList = ({ commit }, stationList) => {
  commit(types.GET_STATION_LIST, stationList);
};

/** 获取 管理台配置 列表 */
export const setAdminConfig = ({ commit }, adminConfig) => {
  commit(types.GET_ADMINCONFIG, adminConfig);
};

export const toLogin = async ({ commit, state }, payLoad) => {
  console.log('🔄 Store toLogin 开始执行:', payLoad);
  commit(types.IS_LOADING, true);
  const language = localStorage.getItem("language") || window.navigator.language;
  const defaultParams = {
    subsystemCode: state.systemCode,
    curLanguage: language,
    module: "PC",
  };
  console.log('🔄 请求参数:', { ...defaultParams, ...payLoad });
  
  try {
    const { code, data } = await axios.post(GET_MODULE_MENUS, {
      ...defaultParams,
      ...payLoad,
    });
    
    console.log('🔄 获取菜单响应:', { code, data });
    if (code) {
      console.error('❌ 获取菜单失败，错误码:', code);
      return Promise.reject(code);
    }
    
    if (!data || !data.user) {
      console.error('❌ 没有获取到当前登录用户数据:', data);
      return Promise.reject('没有获取到当前登录用户');
    }
    
    console.log('✅ 用户数据获取成功:', data.user);
    // 存入localStorage
    // window.localStorage.setItem(
    //   `${state.systemCode}_user`,
    //   JSON.stringify(data.user),
    // );
    window.localStorage.setItem(`${state.systemCode}_cred`, data.sessionId);
  // 保存当前MenuList
  const newMenu = recursion({
    data: data.user.menuList,
    formatter(row) {
      return {
        title: row.name,
        icon: row.icon,
        path: `/${row.code}`,
      };
    },
  });

  // home不纳入权限管理
  const homeMenu = {
    id: "home",
    pid: "-1",
    title: "lang.ark.fed.homepage",
    icon: "iconfont icon-zhifeiji",
    path: "/home",
  };
  newMenu.unshift(homeMenu);
  const dmpRoutes = ["/vensManagement"];
  const vensManagementChildRoutes = [
    "/dmpDeviceModel",
    "/dmpInstance",
    "/dmpTemplateInstance",
    "/dmpTaskManage",
    "/dmpApplyMaintenance",
    "/dmpHeartbeat",
  ];

  let filteredMenu = newMenu;
  if (myConfig.vensVersion !== 3) {
    if (myConfig.vensVersion === 2) {
      filteredMenu = filteredMenu.filter((menu) => !vensManagementChildRoutes.includes(menu.path));
    } else {
      filteredMenu = filteredMenu.filter((menu) => !dmpRoutes.includes(menu.path));
    }
  }
    commit(types.UPDATE_MENU, { menuData: filteredMenu });
    commit(types.UPDATE_USERNAME, { userName: data.user.userName });
    commit(types.UPDATE_CRED, payLoad);
    commit(types.IS_LOADING, false);
    console.log('✅ Store登录流程完成');
    return Promise.resolve(data);
  } catch (error) {
    console.error('❌ Store toLogin 执行出错:', error);
    commit(types.IS_LOADING, false);
    return Promise.reject(error);
  }
};

/*
const logoutArk = () => {
  return new Promise((resolve, reject) => {
    const vm = window.vm;
    const lang = vm.$getLang();
    const xhr = new XMLHttpRequest();
    xhr.open("GET", `${myConfig.arkRoot}/api/coreresource/auth/logout/v1`, true);
    xhr.setRequestHeader("Accept-Language", lang);
    xhr.send();
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          const res = JSON.parse(xhr.responseText);
          if (!res.code) {
            resolve();
          } else {
            reject();
            vm.$message(vm.$t(res.msg));
          }
        } else {
          reject();
        }
      }
    };
  });
};
*/

export const toLogout = async (options, payload) => {
  // await logoutArk();
  // 登出接口
  const headers = {
    "Accept-Language": window.vm.$getLang(),
  };
  const { code } = await axios.get(`${myConfig.authUrl}/api/coreresource/auth/logout/v1`, {
    headers,
    withCredentials: true,
  });
  if (code) return;
  // 触发一下ark退出接口；
  const { isReturnUrl } = payload || { isReturnUrl: true };
  linkAuthMange(isReturnUrl);
};

export const getCommonDict = async ({ commit }, payload = {}) => {
  const { objectCodes } = payload;
  if (!objectCodes || !objectCodes.length) return Promise.reject();
  const { data, code } = await axios.post(apiStore.common.queryDetails.url, {
    enable: true,
    ...payload,
  });
  if (code) return Promise.reject();
  objectCodes.forEach((item) => {
    const dict = data[item];
    let options = [];
    try {
      options = myTransform.arrToOptions(dict.details, "fieldCode", "fieldValue");
    } catch (e) {
      console.error(`${item}缺少当前字典`);
    }
    commit("common", {
      [`${item}_dict`]: options.map((c) => ({
        label: window.vm.$t(c.label),
        value: c.value,
      })),
    });
  });
  return Promise.resolve();
};

export const validateWorkstation = async ({ state }, payload) => {
  try {
    const userName = state.userName;
    // 检查工作站不存在、工作站信息有问题、不可登录
    const { code, data, msg } = await axios.get(IS_LOGIN_STATION, {
      params: { ...payload, userName },
    });
    if (code) return Promise.reject();
    const { code: authCode } = await httpService(apiStore.authWorkStation.checkStationAuth, payload);
    if (authCode) {
      window.vm.$router.push({ name: "manageStation" });
      return Promise.reject();
    }
    return Promise.resolve({ data, msg });
  } catch (e) {
    return Promise.reject();
  }
};

// 获取机器人类型
export const getRobotType = async ({ commit }) => {
  const { data, code } = await httpService(apiStore.flowNew.getRobotClassify);
  if (code) return Promise.reject();
  const options = myTransform.arrToStringOptions(data || [], "v", "k");
  commit("common", { robotClassIfyList: options });
  return Promise.resolve(options);
};

// 判断是否为滚筒工作站
export const validateStationLogin = async ({ commit }) => {
  const { code } = await httpService(apiStore.station.validateStationLogin);
  const workStationVerify = !!code;
  commit("common", { workStationVerify });
  return Promise.resolve(workStationVerify);
};

// 按条件查询所有设备
export const getAllEquipmentList = async ({ commit }) => {
  const { data, code } = await httpService(apiStore.equipment.queryEquipmentList);
  if (code) return Promise.reject();
  commit("common", { allEquipmentList: data });
};
