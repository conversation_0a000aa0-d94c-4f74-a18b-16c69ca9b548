"use strict";
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.
const fs = require("fs");
const path = require("path");

/** 开发测试地址 */
const DEV_SERVER = "************";

const config = fs.readFileSync(path.resolve(__dirname, "../static/config.json"));
const { arkRoot, athenaRoot, authManage } = JSON.parse(config);

module.exports = {
  dev: {
    serverIP: DEV_SERVER,
    // auth的host
    rmsHost: DEV_SERVER,
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/",
    proxyTable: {
      [arkRoot]: {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      [athenaRoot]: {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      [authManage]: {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/static/auth-manage": {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/static/rms": {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/static/ark": {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/venus": {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/athena-monitor": {
        target: "ws://" + DEV_SERVER, //后端目标接口地址
        changeOrigin: true, //是否允许跨域
        secure: false,
        ws: true, //开启ws, 如果是http代理此处可以不用设置
      },
      "/static/wsmp": {
        target: "http://" + DEV_SERVER,
        changeOrigin: true,
      },
      "/static": {
        target: `http://${DEV_SERVER}`,
        changeOrigin: true,
      },
    },
    // Various Dev Server settings
    host: "localhost", // can be overwritten by process.env.HOST
    port: 8888, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-
    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: false,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,
    /**
     * Source Maps
     */
    // https://webpack.js.org/configuration/devtool/#development
    devtool: "cheap-module-eval-source-map",
    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,
    cssSourceMap: true,
  },
  build: {
    // Template for index.html
    index: path.resolve(__dirname, "../ark_build/index.html"),
    // Paths
    assetsRoot: path.resolve(__dirname, "../ark_build"),
    assetsSubDirectory: "static",
    assetsPublicPath: "./",
    /**
     * Source Maps
     */
    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: "#source-map",
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ["js", "css"],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: false,
    speed: false,
  },
};
