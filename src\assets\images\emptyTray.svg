<?xml version="1.0" encoding="UTF-8"?>
<svg width="107px" height="110px" viewBox="0 0 107 110" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 24备份 7</title>
    <defs>
        <path d="M0,7.81835667 L10.692,7.81835667 L10.692,38.43 C10.692,39.5345695 9.7965695,40.43 8.692,40.43 L2,40.43 C0.8954305,40.43 5.7935996e-16,39.5345695 0,38.43 L0,7.81835667 L0,7.81835667 Z" id="path-1"></path>
        <filter x="-93.5%" y="-24.5%" width="287.1%" height="161.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M23.002,8 L33.694,8 L33.694,38.6116433 C33.694,39.7162128 32.7985695,40.6116433 31.694,40.6116433 L25.002,40.6116433 C23.8974305,40.6116433 23.002,39.7162128 23.002,38.6116433 L23.002,8 L23.002,8 Z" id="path-3"></path>
        <filter x="-93.5%" y="-24.5%" width="287.1%" height="161.3%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M47.004,8 L57.696,8 L57.696,38.6116433 C57.696,39.7162128 56.8005695,40.6116433 55.696,40.6116433 L49.004,40.6116433 C47.8994305,40.6116433 47.004,39.7162128 47.004,38.6116433 L47.004,8 L47.004,8 Z" id="path-5"></path>
        <filter x="-93.5%" y="-24.5%" width="287.1%" height="161.3%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M70.506,8.81835667 L81.198,8.81835667 L81.198,38.43 C81.198,39.5345695 80.3025695,40.43 79.198,40.43 L72.506,40.43 C71.4014305,40.43 70.506,39.5345695 70.506,38.43 L70.506,8.81835667 L70.506,8.81835667 Z" id="path-7"></path>
        <filter x="-93.5%" y="-25.3%" width="287.1%" height="163.3%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M37.253,-34.503 L45.945,-34.503 L45.945,-34.503 L45.945,46.695 L37.253,46.695 C36.1484305,46.695 35.253,45.7995695 35.253,44.695 L35.253,-32.503 C35.253,-33.6075695 36.1484305,-34.503 37.253,-34.503 Z" id="path-9"></path>
        <filter x="-93.5%" y="-9.9%" width="287.1%" height="124.6%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M0,7.81835667 L10.692,7.81835667 L10.692,38.43 C10.692,39.5345695 9.7965695,40.43 8.692,40.43 L2,40.43 C0.8954305,40.43 5.7935996e-16,39.5345695 0,38.43 L0,7.81835667 L0,7.81835667 Z" id="path-11"></path>
        <filter x="-93.5%" y="-24.5%" width="287.1%" height="161.3%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M37.004,8 L47.696,8 L47.696,38.6116433 C47.696,39.7162128 46.8005695,40.6116433 45.696,40.6116433 L39.004,40.6116433 C37.8994305,40.6116433 37.004,39.7162128 37.004,38.6116433 L37.004,8 L37.004,8 Z" id="path-13"></path>
        <filter x="-93.5%" y="-24.5%" width="287.1%" height="161.3%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M70.506,8.81835667 L81.198,8.81835667 L81.198,38.43 C81.198,39.5345695 80.3025695,40.43 79.198,40.43 L72.506,40.43 C71.4014305,40.43 70.506,39.5345695 70.506,38.43 L70.506,8.81835667 L70.506,8.81835667 Z" id="path-15"></path>
        <filter x="-93.5%" y="-25.3%" width="287.1%" height="163.3%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M37.253,-34.503 L45.945,-34.503 L45.945,-34.503 L45.945,46.695 L37.253,46.695 C36.1484305,46.695 35.253,45.7995695 35.253,44.695 L35.253,-32.503 C35.253,-33.6075695 36.1484305,-34.503 37.253,-34.503 Z" id="path-17"></path>
        <filter x="-93.5%" y="-9.9%" width="287.1%" height="124.6%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.5 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-25备份-6" transform="translate(12.000000, 52.500000)">
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M10.098,8.41235667 L0.594,8.41235667 L0.594,38.43 C0.594,38.8182562 0.75137191,39.1697562 1.00580787,39.4241921 C1.26024382,39.6786281 1.61174382,39.836 2,39.836 L8.692,39.836 C9.08025618,39.836 9.43175618,39.6786281 9.68619213,39.4241921 C9.94062809,39.1697562 10.098,38.8182562 10.098,38.43 L10.098,8.41235667 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M33.1,8.594 L23.596,8.594 L23.596,38.6116433 C23.596,38.9998995 23.7533719,39.3513995 24.0078079,39.6058355 C24.2622438,39.8602714 24.6137438,40.0176433 25.002,40.0176433 L31.694,40.0176433 C32.0822562,40.0176433 32.4337562,39.8602714 32.6881921,39.6058355 C32.9426281,39.3513995 33.1,38.9998995 33.1,38.6116433 L33.1,8.594 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M57.102,8.594 L47.598,8.594 L47.598,38.6116433 C47.598,38.9998995 47.7553719,39.3513995 48.0098079,39.6058355 C48.2642438,39.8602714 48.6157438,40.0176433 49.004,40.0176433 L55.696,40.0176433 C56.0842562,40.0176433 56.4357562,39.8602714 56.6901921,39.6058355 C56.9446281,39.3513995 57.102,38.9998995 57.102,38.6116433 L57.102,8.594 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M80.604,9.41235667 L71.1,9.41235667 L71.1,38.43 C71.1,38.8182562 71.2573719,39.1697562 71.5118079,39.4241921 C71.7662438,39.6786281 72.1177438,39.836 72.506,39.836 L79.198,39.836 C79.5862562,39.836 79.9377562,39.6786281 80.1921921,39.4241921 C80.4466281,39.1697562 80.604,38.8182562 80.604,38.43 L80.604,9.41235667 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份" transform="translate(40.599000, 6.096000) rotate(-270.000000) translate(-40.599000, -6.096000) ">
                <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M45.351,-33.909 L37.253,-33.909 C36.8647438,-33.909 36.5132438,-33.7516281 36.2588079,-33.4971921 C36.0043719,-33.2427562 35.847,-32.8912562 35.847,-32.503 L35.847,44.695 C35.847,45.0832562 36.0043719,45.4347562 36.2588079,45.6891921 C36.5132438,45.9436281 36.8647438,46.101 37.253,46.101 L45.351,46.101 L45.351,-33.909 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
        </g>
        <g id="编组-24备份-7">
            <rect id="矩形" fill="#CDA000" x="0" y="2.376" width="106.92" height="106.92" rx="9.504"></rect>
            <rect id="矩形" fill="#FFCE1F" x="0" y="0" width="106.92" height="106.92" rx="9.504"></rect>
        </g>
        <g id="编组-25备份-7" transform="translate(12.000000, 55.000000)">
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M10.098,8.41235667 L0.594,8.41235667 L0.594,38.43 C0.594,38.8182562 0.75137191,39.1697562 1.00580787,39.4241921 C1.26024382,39.6786281 1.61174382,39.836 2,39.836 L8.692,39.836 C9.08025618,39.836 9.43175618,39.6786281 9.68619213,39.4241921 C9.94062809,39.1697562 10.098,38.8182562 10.098,38.43 L10.098,8.41235667 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M47.102,8.594 L37.598,8.594 L37.598,38.6116433 C37.598,38.9998995 37.7553719,39.3513995 38.0098079,39.6058355 C38.2642438,39.8602714 38.6157438,40.0176433 39.004,40.0176433 L45.696,40.0176433 C46.0842562,40.0176433 46.4357562,39.8602714 46.6901921,39.6058355 C46.9446281,39.3513995 47.102,38.9998995 47.102,38.6116433 L47.102,8.594 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M80.604,9.41235667 L71.1,9.41235667 L71.1,38.43 C71.1,38.8182562 71.2573719,39.1697562 71.5118079,39.4241921 C71.7662438,39.6786281 72.1177438,39.836 72.506,39.836 L79.198,39.836 C79.5862562,39.836 79.9377562,39.6786281 80.1921921,39.4241921 C80.4466281,39.1697562 80.604,38.8182562 80.604,38.43 L80.604,9.41235667 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="矩形备份" transform="translate(40.599000, 6.096000) rotate(-270.000000) translate(-40.599000, -6.096000) ">
                <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                <path stroke="#00002D" stroke-width="1.188" d="M45.351,-33.909 L37.253,-33.909 C36.8647438,-33.909 36.5132438,-33.7516281 36.2588079,-33.4971921 C36.0043719,-33.2427562 35.847,-32.8912562 35.847,-32.503 L35.847,44.695 C35.847,45.0832562 36.0043719,45.4347562 36.2588079,45.6891921 C36.5132438,45.9436281 36.8647438,46.101 37.253,46.101 L45.351,46.101 L45.351,-33.909 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
        </g>
    </g>
</svg>