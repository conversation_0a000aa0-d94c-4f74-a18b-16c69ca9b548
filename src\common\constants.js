import fp from "lodash/fp";
import { map } from "lodash";
import { useI18n } from "@/hooks/use-i18n";
const t = useI18n();

/**
 * 常量封装类
 *
 * 用法
   ------------------
   constantList([
     {
        key: "SPECIFIED",
        value: "1",
        labelI18nKey: "lang.ark.dynamicTemplate.previousNode",
     }
   ])

   属性:
   - key: 必填, 必须大写, ConstantList 实例中唯一 key , 会挂载到实例对象上, 可在实例对象上直接访问
   - value: 必填, 常量值
   - labelI18nKey: 可选, i18n key 值, 会自动翻译并存放到 label 属性上
 */
class ConstantList {
  constructor(list = []) {
    this._list = fp.cloneDeep(list);
    this.forEach((item) => {
      this[String(item.key).toUpperCase()] = item.value;
    });

    /**
     * 初始化 i18n 国际化字符
     */
    this.initI18n = fp.once(() => {
      this.forEach((item) => {
        if (item.labelI18nKey != null) {
          item.label = t(item.labelI18nKey);
        }
      });
    });
    return this;
  }

  /**
   * 遍历元素
   *
   * @param  {function} 遍历函数: (item, index, array)=> {}
   * @returns this
   */
  forEach(...args) {
    this._list.forEach(...args);
    return this;
  }

  /**
   *  映射, 同 lodash.map()
   *
   * @returns array
   */
  map(...args) {
    // 这里没有用 fp.map() 是为了保证遍历函数参数为三个: (item, index, array) => {}
    // fp.map() 被 curried 的之后参数为一个: (item) => {}
    return map(this._list, ...args);
  }

  /**
   *  查找元素或属性
   *
   * @param  {any} predicate 查找条件, 同 lodash.find()
   * @param  {string} property 访问属性, 可选, 有此参数时返回元素上对应的属性值
   */
  find(condition, property) {
    if (property === "label") this.initI18n();
    const result = fp.find(condition, this._list);
    return property ? fp.get(property, result) : result;
  }

  /**
   * 通过 value 查找对应的 label
   *
   * @param {number|string} value
   * @returns
   */
  getLabelByValue(value) {
    this.initI18n();
    return fp.pipe(
      fp.find((item) => String(item.value) === String(value)),
      fp.get("label")
    )(this._list);
  }

  /**
   * 判断是否存在对应 value
   *
   * @param {number|string} value
   * @returns value
   */
  includesValue(value) {
    return this.find({ value }) !== undefined;
  }

  /**
   * 返回常量元数据数组
   *
   * @returns data list
   */
  toArray() {
    return fp.cloneDeep(this._list);
  }

  /**
   * 返回 [{label, value}] 数组, 可用于填充 select 选项数据.
   *
   * @returns array
   */
  toLabelValueList() {
    this.initI18n();
    return fp.pipe(
      fp.cloneDeep, //
      fp.map(fp.pick(["label", "value"]))
    )(this._list);
  }
}

export function constantList(...args) {
  return new ConstantList(...args);
}

/**
 * 单元格类型
 */
export const WFChartCellTypes = constantList([
  { key: "STATION_CELL", value: 1 }, // 停靠点
  { key: "WORKSTATION_CELL", value: 2 }, // 工作站
  { key: "SHELF_CELL", value: 3 }, // 货架点
  { key: "AREA_CELL", value: 4 }, // 货架区
  { key: "WAIT_POINT_CELL", value: 5 }, // 等待点
  { key: "WORKFLOW_POINT_CELL", value: 6 }, // 流程节点（子流程）子任务
  { key: "VIRTUAL_POINT_CELL", value: 7 }, // 虚拟点
  { key: "REST_POINT_CELL", value: 8 }, // 待命点
  { key: "CACHE_POINT_CELL", value: 9 }, // 暂存区点(SLAM本地用)
  { key: "GENERAL_POINT_CELL", value: 10 }, // 地图节点, 通用节点(SLAM本地用)
  { key: "AUTO_ASSIGNMENT_NODE", value: 11 }, // 自动赋值点
  { key: "EQUIPMENT_NODE", value: 12 }, // 设备节点
  { key: "PALLET_POINT", value: 100 }, // 托盘位
]);

/**
 *  流程节点类型
 */
export const WFChartNodeTypes = constantList([
  { key: "END_NODE", value: 0 }, // 结束节点
  { key: "BEGIN_NODE", value: 1 }, // 开始节点
  { key: "ELEMENT_NODE", value: 2 }, // 元素节点
  { key: "COMMON_NODE", value: 3 }, // 通用节点
  { key: "BUSINESS_NODE", value: 4 }, // 业务节点
  { key: "START_NODE", value: 5 }, // 起点
  { key: "MID_NODE", value: 6 }, // 中间点
  { key: "FINISH_NODE", value: 7 }, // 终点
  { key: "ORDER_NODE", value: 8 }, // 顺序节点(接口赋值点)
  { key: "SPECIFIC_START_NODE", value: 9 }, // 定制起点
  { key: "SPECIFIC_FINISH_NODE", value: 10 }, // 定制终点
  { key: "AUTO_ASSIGNMENT_NODE", value: 11 }, // 自动赋值点
]);

/**
 *   料口类型
 */
export const feedGateCategories = constantList([
  // 取料口
  {
    key: "OUTTAKE",
    value: "1",
    labelI18nKey: "lang.ark.fed.reclaimer",
  },
  // 放料口
  {
    key: "INTAKE",
    value: "2",
    labelI18nKey: "lang.ark.fed.dischargePort",
  },
]);

/**
 *    流程模板类型
 */
export const workflowTemplateTypes = constantList([
  // 点到点任务
  {
    key: "P_TO_P",
    value: 10,
    labelI18nKey: "lang.ark.workflow.template.type.nodeToNode",
  },
  // 点到多点任务
  {
    key: "P_TO_PS",
    value: 20,
    labelI18nKey: "lang.ark.workflow.template.type.nodeToMultiNode",
  },
  // 动态点任务
  {
    key: "DYNAMIC_P",
    value: 30,
    labelI18nKey: "lang.ark.workflow.template.type.dynamiNode",
  },
  // 点到多点定制任务
  {
    key: "CUSTOMIZED_P_TO_PS",
    value: 40,
    labelI18nKey: "lang.ark.fed.multiNodeCustom",
  },
]);

/**
 * 流程图中模板节点的唯一 key 标识
 */
export const WFChartTemplateNodeKeys = constantList([
  // 动态点模板 -> 提升机流程 -> 动态赋值点 1
  {
    key: "DYNAMIC_TMP_ELEVATOR_FLOW_AUTO_ASSIGN_NODE_1",
    value: "dc62100e-12cd-429c-a6b4-25881ecc0113",
  },
  // 动态点模板 -> 提升机流程 -> 动态赋值点 2
  {
    key: "DYNAMIC_TMP_ELEVATOR_FLOW_AUTO_ASSIGN_NODE_2",
    value: "4654f216-3c62-463d-832e-98ffb28897d9",
  },
]);

/**
 * 流程图模板中边线对应唯一 key 标识
 */
export const WFChartTemplateEdgeKeys = constantList([
  // 动态点模板 -> 提升机流程 -> 边: 1 to 2
  {
    key: "DYNAMIC_TMP_ELEVATOR_FLOW_EDGE_1_TO_2",
    value: "ad01726a-122c-4a04-868d-4a9eea35758a",
  },
  // 动态点模板 -> 提升机流程 -> 边: 1 to 4
  {
    key: "DYNAMIC_TMP_ELEVATOR_FLOW_EDGE_1_TO_4",
    value: "d527fea4-70d3-4338-9ac3-17625ffdeea1",
  },
]);

/**
 * 流程图中节点边的类型
 */
export const WFChartEdgeTypes = constantList([
  // 机器人任务流转
  {
    key: "ROBOT_TASK",
    value: "0",
    labelI18nKey: "lang.ark.fed.robotTaskFlow",
  },
  // 业务规则流转线
  {
    key: "BIS",
    value: "1",
    labelI18nKey: "lang.ark.fed.businessRuleFlow",
  },
  // 设备任务流转线
  {
    key: "EQUIPMENT_TASK",
    value: "2",
    labelI18nKey: "lang.ark.fed.component.workflow.edgeName.equipmentTask",
  },
]);

/**
 * 流程图中节点边的锚点
 */
export const WFChartEdgeAnchorTypes = constantList([
  { key: "RIGHT", value: 1 }, //  节点右边
  { key: "BOTTOM", value: 2 }, //  节点下边
  { key: "LEFT", value: 3 }, //  节点左边
  { key: "TOP", value: 4 }, //  节点上边
]);

/**
 * 流程图中节点边的属性
 */
export const WFChartEdgeProperties = constantList([
  { key: "START_TO_NODE", value: "0" }, // 开始节点  -> 流程节点:   segmentConfig 0 不显示属性面板。
  { key: "NODE_TO_END", value: "1" }, // 流程节点 ->  结束节点:   segmentConfig: 1 不显示属性面板。
  { key: "OTHER_TO_NODE", value: "2" }, // 其他类型节点 -> 流程节点 segmentConfig: 2  触发时机只能显示上一步，下一步。
  { key: "NODE_TO_NODE", value: "3" }, // 流程节点 -> 流程节点 segmentConfig: 3,  触发时机: 结束。
]);

/**
 * 流程图中节点边: 规则名称的唯一 key 值
 */
export const WFChartEdgeRuleKeys = constantList([
  //  默认规则
  {
    key: "DEFAULT",
    value: "id_3ffc323c-090f-4e63-8c29-d367ced55813",
  },
  // 起、终点楼层相同
  {
    key: "TO_CURRENT_FLOOR",
    value: "id_1048acce-ba4f-4a1b-9a50-288197a7cf66",
  },
  // 起、终点楼层不同
  {
    key: "TO_DIFFERENT_FLOOR",
    value: "id_c158be7a-89d0-418e-8b19-6f0c37691970",
  },
]);

/**
 * 流程任务状态
 */
export const WFTaskStatus = constantList([
  //预备
  { key: "READY", value: "0", labelI18nKey: "lang.ark.workflow.task.status.ready" },
  //创建
  { key: "CREATE", value: "10", labelI18nKey: "lang.ark.workflow.task.status.create" },
  //等待排队机器人
  { key: "WAIT_QUEUE_ROBOT", value: "13", labelI18nKey: "lang.ark.workflow.task.status.wait.queue.robot" },
  //任务暂挂
  { key: "SUSPENSION", value: "14", labelI18nKey: "lang.ark.workflow.task.status.suspension" },
  //等待
  { key: "WAITING", value: "15", labelI18nKey: "lang.ark.workflow.task.status.wait" },
  //任务已下发
  { key: "TASK_SENT", value: "16", labelI18nKey: "lang.ark.workflow.task.status.assign" },
  //机器人已上路
  { key: "MOVING", value: "17", labelI18nKey: "lang.ark.workflow.task.status.moving" },
  //取到容器
  { key: "FETCHED", value: "18", labelI18nKey: "lang.ark.workflow.task.status.fetched" },
  //执行中
  { key: "EXECUTING", value: "20", labelI18nKey: "lang.ark.workflow.task.status.executing" },
  //中断等待
  { key: "INTERRUPT_WAITING", value: "21", labelI18nKey: "lang.ark.workflow.task.status.InterruptWaiting" },
  //指令执行中
  { key: "COMMAND_EXECUTING", value: "22", labelI18nKey: "lang.ark.workflow.task.status.commandExecuting" },
  //外部设备运输中
  { key: "DEVICE_EXECUTING", value: "24", labelI18nKey: "lang.ark.workflow.task.status.deviceExecuting" },
  //节点等待中
  { key: "NODE_WAITING", value: "25", labelI18nKey: "lang.ark.workflow.task.status.node.wait" },
  //任务暂停
  { key: "PAUSE", value: "26", labelI18nKey: "lang.ark.workflow.task.status.node.pause" },
  //撤销中
  { key: "UNDOING", value: "27", labelI18nKey: "lang.ark.workflow.task.status.node.undoing" },
  //退回中
  { key: "BACKING", value: "28", labelI18nKey: "lang.ark.workflow.task.status.node.backing" },
  //执行取消中
  { key: "CANCEL_EXECUTING", value: "29", labelI18nKey: "lang.ark.workflow.task.status.cancelExecuting" },
  //完成
  { key: "COMPLETED", value: "30", labelI18nKey: "lang.ark.workflow.task.status.completed" },
  //异常完成
  { key: "EXCEPTION_COMPLETED", value: "31", labelI18nKey: "lang.ark.workflow.task.status.exception.completed" },
  //取消完成
  { key: "CANCELED", value: "32", labelI18nKey: "lang.ark.workflow.task.status.canceled" },
  //送到指定点
  { key: "CANCELED_TO_LOCATION", value: "33", labelI18nKey: "lang.ark.workflow.task.status.cancelToNewAssignation" },
]);

/**
 * 流程表单: 指定节点类型选项
 */
export const WFSpecifyNodeTypeOptions = constantList([
  // 指定
  { key: "SPECIFIED", value: "1", labelI18nKey: "lang.ark.fed.component.workflow.label.specified" },
  // 不指定
  { key: "NON_SPECIFIED", value: "0", labelI18nKey: "lang.ark.fed.component.workflow.label.nonSpecified" },
]);

/**
 * 流程表单: 逻辑控制选项
 */
export const WFLogicControlOptions = constantList([
  // 上一个点
  { key: "PREVIOUS_NODE", value: "1", labelI18nKey: "lang.ark.dynamicTemplate.previousNode" },
  // 下一个点
  { key: "NEXT_NODE", value: "2", labelI18nKey: "lang.ark.dynamicTemplate.nextPoint" },
]);

/** 命中策略词典 */
export const HitStrategyDict = constantList([
  // 默认策略
  { key: "DEFAULT", value: "1" },
  // 先进先出
  { key: "QUEUE", value: "2" },
  // 后进先出
  { key: "STACK", value: "3" },
  // 最短距离
  { key: "SHORTEST", value: "4" },
  // 循环存放
  { key: "CYCLIC", value: "5" },
  // 托盘密集存储
  { key: "PALLET_DENSE", value: "6" },
  // 支架长廊密集存储
  { key: "SHELF_DENSE", value: "7" },
]);

/** 排队策略字典 */
export const QueueStrategyDict = constantList([
  // 顺序排队
  { key: "ORDER", value: "1" },
  // 队尾排队
  { key: "END", value: "2" },
  // 不排队
  { key: "NO_ORDER", value: "3" },
]);

/** 区域功能类型字典 */
export const AreaFunctionTypeDict = constantList([
  // 交通管制区
  { key: "TRAFFIC", value: "10" },
  // 存储区
  { key: "STORAGE", value: "20" },
  // 排队区
  { key: "QUEUE", value: "30" },
  // 急停区
  { key: "STOP", value: "40" },
  // 人工管制区
  { key: "MANUAL", value: "50" },
  // 管理区
  { key: "MANAGE", value: "60" },
  // 清洗区
  { key: "EMPTYING", value: "70" },
]);

/** 容器状态字典 */
export const ShelfStatusDict = constantList([
  // 离场
  { key: "DEPARTURE", value: 0 },
  // 空闲
  { key: "IDLE", value: 1 },
  // 工作中
  { key: "WORKING", value: 2 },
]);

/** 区域点位类型字典 */
export const AreaNodeTypeDict = constantList([
  // 基础点位
  { key: "BASE", value: "base", labelI18nKey: "lang.ark.fed.selectPoints", useButton: true },
  // 托盘位点位
  { key: "PALLET", value: "pallet", labelI18nKey: "lang.ark.fed.selectPalletPosition", useButton: true },
]);

/**
 * 系统配置，参数值组件类型
 */
export const SCComponentType = constantList([
  { key: "1", value: "elInput" }, // 输入框组件。
  { key: "2", value: "elSwitch" }, // 开关组件
  { key: "3", value: "elRadioGroup" }, // 单选框组件
  { key: "4", value: "elSelect" }, // 下拉选
]);

/**
 * 系统配置，参数分组
 */
export const SCSystemGroup = constantList([
  { key: "COMMON", value: "COMMON", labelI18nKey: "lang.ark.fed.screen.systemConfig.commonGroup" }, // 系统配置
  { key: "BUSINESS", value: "BUSINESS", labelI18nKey: "lang.ark.fed.screen.systemConfig.businessGroup" }, // 业务功能
]);
