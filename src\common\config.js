/* eslint-disable */
import * as constants from "../constants/commonConstants";

// 暂时注释 zabbix，观察期一年，如果线上正常，删除 zabbix
// import "@/utils/zabbix-alert-ui.js";

let root = "";

let config = {};
let wsDevIP = "";
if (process.env.NODE_ENV === constants.DEV_ENV) {
  wsDevIP = serverIP;
}

function removeComment(src) {
  const reg = /("([^\\\"]*(\\.)?)*")|('([^\\\']*(\\.)?)*')|(\/{2,}.*?(\r|\n|$))|(\/\*(\n|.)*?\*\/)/g;
  return src.replace(reg, (word) => (/^\/{2,}/.test(word) || /^\/\*/.test(word) ? "" : word));
}

function parseConfig(data) {
  const config = {};
  data.forEach((confInfo) => {
    switch (confInfo.sysType.toLocaleLowerCase()) {
      case "string":
        config[confInfo.sysKey] = confInfo.sysValue;
        break;
      case "boolean":
        config[confInfo.sysKey] = confInfo.sysValue.toLocaleLowerCase() === "true";
        break;
      case "int":
      case "integer":
        config[confInfo.sysKey] = parseInt(confInfo.sysValue, 10);
        break;
      case "float":
        config[confInfo.sysKey] = parseFloat(confInfo.sysValue, 10);
        break;
      case "array":
      case "object":
        config[confInfo.sysKey] = JSON.parse(confInfo.sysValue);
        break;
      default:
        console.warn("config:", confInfo.sysKey, "无法识别类型:", confInfo.sysType);
        break;
    }
  });
  return config;
}

try {
  var xhr = new XMLHttpRequest();

  xhr.open("GET", `${root}/ark-web/api/sysconfig/querySysConfigList`, false);
  xhr.onreadystatechange = function () {
    var XMLHttpReq = xhr;
    if (XMLHttpReq.readyState == 4) {
      if (XMLHttpReq.status == 200) {
        var text = XMLHttpReq.responseText;
        var json = JSON.parse(removeComment(text));
        if (json.code === 0) {
          config = { ...config, ...parseConfig(json.data) };
        } else {
          console.error(json.msg);
        }
      }
    }
  };
  xhr.send();
} catch (e) {
  console.error(e);
}

wsDevIP = wsDevIP || window.location.hostname;
config.RMSURL = config.RMSURL.replace(/\:\/\/[^\:]+\:/, `://${wsDevIP}:`);
config.STATION_SERVICE = config.STATION_SERVICE.replace(/\:\/\/[^\:]+\:/, `://${wsDevIP}:`);
config.ROOT = root;

// 处理auth-manage相关接口的域名
if (config.authUrl && config.authUrl.startsWith('/')) {
  config.authUrl = `http://auth-manage${config.authUrl}`;
}

/**
 * 内网监控报警, 监控网速的
 * 依赖于 @/utils/zabbix-alert-ui.js
 */
// 暂时注释 zabbix，观察期一年，如果线上正常，删除 zabbix
/*
try {
  // 初始化zabbix插件
  const gZabbixConf = {
    // zabbix的服务ip地址
    zabbixIp: wsDevIP,

    // 登陆信息
    user: "readonly",
    password: "readonly",
    // 弹出异常弹框的最低警告等级
    alertSeverity: ["3"],
    // 请求服务器状态轮询时间
    getAlertTime: 3000,
    // 关闭后的重启时间 10 分钟
    closeTime: 600000,
    // 弹出层一般配置
    params: {
      severities: ["4", "3", "2"],
      severitieNames: ["严重", "一般严重", "警告"],
      severitieCloseTime: [
        10 * 60 * 1000, // 严重 10分钟
        20 * 60 * 1000, // 一般严重 20分钟
        60 * 60 * 1000, // 警告 60分钟
      ],
      severitieTypeName: ["danger", "danger", "warning"],
    },
  };

  new GZabbix(gZabbixConf);
} catch (error) {
  console.log("GZabbix Error", error);
}
*/

export default config;
