﻿<html> 
<head> 
<title>JavaScript串口测试</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="x-ua-compatible" content="IE=7,8,9,10" >

<SCRIPT LANGUAGE=javascript FOR=EElink_Com EVENT="onOutput(value)">
    //数据接收显示
    //alert(value);
  try{
      document.getElementById("txtReceive").value += value;
      }catch(e)
      {
        alert("出错！");
      }    
</SCRIPT>

<SCRIPT ID=clientEventHandlersJS   LANGUAGE=javascript> 
<!--
  function OperatePort()  //打开和关闭串口
  {
     if(document.getElementById("OperateButton").value=="打开串口")
     {
        document.getElementById("OperateButton").value="关闭串口";
        EElink_Com.commPort = document.getElementById("ComName").value;
        EElink_Com.openCom();
     }
     else
     {  
        document.getElementById("OperateButton").value="打开串口";
        EElink_Com.clossCom();
     }
  }
  
   function Send()
   {
     if(document.getElementById("OperateButton").value=="关闭串口")
      {
        EElink_Com.inputMsg(document.getElementById("txtSend").value);//数据格式是16进制字符串
      }
     else
     {
       alert("串口关闭状态，无法发送数据！");
     } 
   }
 
   function Sg_Send(sg_type)
   {
     if(document.getElementById("OperateButton").value=="关闭串口")
      {
        switch(sg_type)
        {
        case 0:document.getElementById("txtSend").value="0110001A0001000FD8";
             break;
        case 1:document.getElementById("txtSend").value="0110001A0001028E19";
             break;
        case 2:document.getElementById("txtSend").value="0110001A000101CE18";
             break;
        case 3:document.getElementById("txtSend").value="0110001A0001040E1B";
             break;
        case 4:document.getElementById("txtSend").value="0110001A0001034FD9";
             break;
        case 5:document.getElementById("txtSend").value="0110001A000105CFDB";
             break;        
        }
         EElink_Com.inputMsg(document.getElementById("txtSend").value);//数据格式是16进制字符串
      }
     else
     {
       alert("串口关闭状态，无法发送数据！");
     } 
   }
   
   function ClearReceived()  //清空接收内容
   {
     document.getElementById("txtReceive").innerText="";
   } 
 --> 
</script>   
 <script>
     window.onbeforeunload = function(e)
    {             
                e.returnValue = '数据提交是否退出?';
     };
 </script>
</head> 
<body>
<div class="style1">
<form name="form1"><br>
	串口调试软件<br>
<table style="width: 81%" align="center">
<tr>
		<td>
		  <fieldset style="width:300px;height:250px;text-align:center;">
          <legend>接收区域</legend>   
          <div style="float:left;" class="style1">
          <textarea id="txtReceive" READONLY=TRUE  name="txtReceive" style="width:300px;height:160px"></textarea>  
          <br/>
          <span><input id="isReceiveHex" name="isReceiveHex" type="checkbox" />16进制</span>
          <input  type="button" id="ClearButton" style="width:100px;height:30px"   name="ClearButton"   value="清空"   onClick="ClearReceived()">
          </div>
          </fieldset> 
		  </td>
		<td style="width: 310px">
	  	  <fieldset style="width:300px;height:250px;text-align:center;">
          <legend>发送区域</legend>
          <div style="float:left;">
          <textarea id="txtSend"  name="txtSend" style="width:300px; height:160px"></textarea> 
          <br/>
          <span>请用16进制输入</span>
          <input   type="button" id="SendButton" style="width:100px;height:30px"   name="SendButton"   value="发送"   onClick="Send()">   
          </div> 
          </fieldset>
		</td>
		<td>		
		  <fieldset style="width:200px;height:250px;text-align:center;">
		  <legend>配置串口</legend>
		  <div style="float:left;width:200px">   
          <br/>   
          <span>串口号:</span>
          <select name="ComName" id="ComName" style="width:75px" >
          <option value="1"  >COM1</option>
          <option value="2"  >COM2</option>
          <option value="3"  >COM3</option>
          <option value="4"  >COM4</option>   
          <option value="5"  >COM5</option>    
          <option value="6" selected>COM6</option>  
          </select>   
          <br/>   
          <span>波特率:</span>
          <select name="BaudRate" id="BaudRate" style="width:75px" >
          <option value="9600" selected  >9600</option>
          <option value="57600"  >57600</option>
          <option value="115200" >115200</option>   
          </select>   
          <br/>
          <span>校验位:</span>
          <select name="CheckBit" id="CheckBit" style="width:75px" >
          <option value="N" selected  >无NONE</option>
          <option value="O"  >奇ODD</option>
          <option value="E" >偶EVEN</option>   
          </select>   
          <br/>   
          <span>数据位:</span>
          <input type=text id="DataBits" name="DataBits" value=8 style="width:75px;height:20px">
          <br/>
          <span>停止位:</span>
          <input type=text id="StopBits" name="StopBits" value=1 style="width:75px;height:20px">
          <br/>
          <br/><input   type="button" id="OperateButton" style="width:80px;height:30px;font-size:13px"   name="OperateButton"   value="打开串口"   onClick="OperatePort()">       
          </div> 
          </fieldset>
		</td>
	</tr>	 
	<tr>
	  <td colspan="3" class="style1">
          <input   type="button" id="Sg_Send1" style="width:80px;height:30px"   name="Sg_Send1"   value="闪光"   onClick="Sg_Send(1)">&nbsp;
          <input   type="button" id="Sg_Send2" style="width:80px;height:30px"   name="Sg_Send2"   value="闪光+声音1"   onClick="Sg_Send(2)">&nbsp;
          <input   type="button" id="Sg_Send3" style="width:80px;height:30px"   name="Sg_Send3"   value="闪光+声音2"   onClick="Sg_Send(3)">&nbsp;
          <input   type="button" id="Sg_Send4" style="width:80px;height:30px"   name="Sg_Send4"   value="声音1"   onClick="Sg_Send(4)">&nbsp;
          <input   type="button" id="Sg_Send5" style="width:80px;height:30px"   name="Sg_Send5"   value="声音2"   onClick="Sg_Send(5)">&nbsp;
          <input   type="button" id="Sg_Send0" style="width:80px;height:30px"   name="Sg_Send0"   value="关闭"   onClick="Sg_Send(0)"></td>
	</tr>
</table>
</form> 
</div>
</body> <OBJECT id="EElink_Com" name="EElink_Com" classid="clsid:233AE5BD-1C51-4A87-A359-7C6CE3B7DD67"  
type="application/x-oleobject" style="width: 69px; height: 50px">
</OBJECT>
</html>
